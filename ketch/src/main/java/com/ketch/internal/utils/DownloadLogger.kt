package com.ketch.internal.utils

import android.util.Log
import com.ketch.LogType
import com.ketch.Logger

internal class DownloadLogger : Logger {
    override fun log(tag: String?, msg: String?, tr: Throwable?, type: LogType) {
        when (type) {
            LogType.VERBOSE -> Log.v(tag, msg, tr)
            LogType.DEBUG -> Log.d(tag, msg, tr)
            LogType.INFO -> Log.i(tag, msg, tr)
            LogType.WARN -> Log.w(tag, msg, tr)
            LogType.ERROR -> Log.e(tag, msg, tr)
        }
    }
}
