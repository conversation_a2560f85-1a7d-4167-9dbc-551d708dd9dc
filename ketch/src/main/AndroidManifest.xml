<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.WAKE_LOCK"
        tools:node="replace" />

    <application>
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            tools:node="merge" />

        <receiver
            android:name=".notification.NotificationReceiver"
            android:exported="false" />
    </application>

</manifest>