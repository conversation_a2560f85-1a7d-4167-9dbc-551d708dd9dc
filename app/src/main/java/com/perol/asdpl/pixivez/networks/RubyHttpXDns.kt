/*
 * MIT License
 *
 * Copyright (c) 2019 Perol_Notsfsssf
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE
 */

package com.perol.asdpl.pixivez.networks

import com.perol.asdpl.pixivez.objects.CrashHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.Dns
import java.net.InetAddress

object RubyHttpXDns : Dns {
    private val addressCache = mutableMapOf<String, InetAddress>()
    private val addressCacheX = mutableMapOf<String, List<InetAddress>>()
    private val ip_regex =
        "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}".toRegex()
    private val apiAddress = mapOf(
        "app-api.pixiv.net" to "***************",
        "oauth.secure.pixiv.net" to "***************",
        "accounts.pixiv.net" to "***************",
        "s.pximg.net" to "***************",
        "i.pximg.net" to "***************",
        "imgaz.pixiv.net" to "***************",
        "sketch.pixiv.net" to "***************",
        "www.pixiv.net" to "***************",
        //"www.recaptcha.net" to "*************",
        //"www.gstatic.cn" to "*************",
    )

    /*
    D/httpdns addressList: {app-api.pixiv.net=[app-api.pixiv.net.cdn.cloudflare.net./*************, app-api.pixiv.net.cdn.cloudflare.net./*************, /*************, /*************], oauth.secure.pixiv.net=[oauth.secure.pixiv.net.cdn.cloudflare.net./*************, oauth.secure.pixiv.net.cdn.cloudflare.net./*************, /*************, /*************], accounts.pixiv.net=[accounts.pixiv.net.cdn.cloudflare.net./*************, accounts.pixiv.net.cdn.cloudflare.net./*************, /*************, /*************], s.pximg.net=[/**************, /**************, /**************, /**************, /**************, /**************, /**************, /**************, /**************, /**************], i.pximg.net=[/**************, /**************, /**************, /**************, /**************, /**************, /**************, /**************, /**************, /**************], imgaz.pixiv.net=[/***************, /***************, /***************, /***************], sketch.pixiv.net=[/**************, /***************, /***************], www.pixiv.net=[www.pixiv.net.cdn.cloudflare.net./*************, www.pixiv.net.cdn.cloudflare.net./*************, /*************, /*************]}
D/httpdns addressListX: {app-api.pixiv.net=[app-api.pixiv.net/**************, app-api.pixiv.net/2001::45ab:ef0b], oauth.secure.pixiv.net=[oauth.secure.pixiv.net/***********, oauth.secure.pixiv.net/2001::9a55:661e], accounts.pixiv.net=[accounts.pixiv.net/***************, accounts.pixiv.net/2001::453f:b00b], s.pximg.net=[s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************, s.pximg.net/**************], i.pximg.net=[i.pximg.net/***************, i.pximg.net/2001::6ca0:a36a], imgaz.pixiv.net=[imgaz.pixiv.net/***************, imgaz.pixiv.net/2001::48e8:aa10], sketch.pixiv.net=[sketch.pixiv.net/**********, sketch.pixiv.net/2001::7a0a:5504], www.pixiv.net=[www.pixiv.net/**********, www.pixiv.net/2001::453f:ba1e]}
D/httpdns init end: ========================================
D/httpdns: [app-api.pixiv.net.cdn.cloudflare.net./*************, oauth.secure.pixiv.net.cdn.cloudflare.net./*************, accounts.pixiv.net.cdn.cloudflare.net./*************, /**************, /**************, /***************, /**************, www.pixiv.net.cdn.cloudflare.net./*************]
     */
    private var inited = false
    override fun lookup(hostname: String): List<InetAddress> {
        if (!inited) {
            inited = true
            //  CrashHandler.instance.d("httpdns init", "========================================")
            //  CrashHandler.instance.d("httpdns", dlookup().toString())
            //  CrashHandler.instance.d("httpdns", "========================================")
            for ((host, ip) in apiAddress) {
                InetAddress.getByName(ip).also {
                    // addressCache[host]= it
                    addressCacheX[host] = listOf(it)
                }
            }
        }
        // return if full ip
        if (hostname.matches(ip_regex)) {
            return listOf(InetAddress.getByName(hostname))
        }
        /*try {
            InetAddress.getByName(hostname).also {
                if (it.hostAddress.equals(hostname))
                    return listOf(it)
            }
        }catch (e: UnknownHostException){
             CrashHandler.instance.d("httpdns", "UnknownHostException $e")

        }*/
        // if (addressCache.contains(hostname))
        //    return listOf(addressCache[hostname]!!)
        if (addressCacheX.contains(hostname)) {
            return addressCacheX[hostname]!!
        }
        // if (addressList.isNotEmpty()) return addressList
        CrashHandler.instance.d("httpdns", "========================================")
        val addressList = mutableListOf<InetAddress>()

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val response = ServiceFactory.CFDNS.lookup(hostname)
                addressList.addAll(response)
            } catch (e: Exception) {
                addressList.addAll(Dns.SYSTEM.lookup(hostname))
            }
        }
        CrashHandler.instance.d("httpdns", addressList.toString())
        CrashHandler.instance.d("httpdns end", "========================================")
        if (addressList.isNotEmpty()) {
            addressCache[hostname] = addressList[0]
            addressCacheX[hostname] = addressList
        }
        return addressList
    }
}
