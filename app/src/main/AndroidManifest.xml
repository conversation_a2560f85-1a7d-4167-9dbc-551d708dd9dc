<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <application
        android:name=".services.PxEZApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher_blue"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:enableOnBackInvokedCallback="true"
        android:theme="@style/AppThemeBase"
        tools:replace="android:theme">

        <meta-data
            android:name="com.perol.asdpl.pixivez.services.CustomGlideModule"
            android:value="GlideModule" />

        <activity
            android:name=".ui.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name=".pure"
            android:enabled="true"
            android:exported="true"
            android:icon="@mipmap/ic_launcher_blue"
            android:label="@string/app_name"
            android:targetActivity=".ui.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".md"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_launchermd"
            android:label="@string/app_name"
            android:targetActivity=".ui.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".triangle"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_launcherep"
            android:label="@string/app_name"
            android:targetActivity=".ui.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".normal"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:targetActivity=".ui.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name=".ui.FragmentActivity"
            android:exported="true"
            android:resizeableActivity="true"
            android:theme="@style/AppThemeBase.NoActionBar"
            tools:ignore="UnusedAttribute" />
        <activity android:name=".ui.account.NewUserActivity" />
        <activity
            android:name=".ui.account.LoginActivity"
            android:exported="true" />
        <activity
            android:name=".ui.user.UserMActivity"
            android:exported="true" />
        <activity android:name=".ui.manager.DownloadManagerActivity" />
        <activity
            android:name=".ui.manager.ImgManagerActivity"
            android:exported="true" />
        <activity android:name=".ui.WebViewActivity" />
        <activity android:name=".ui.OKWebViewActivity" />
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:label="@string/title_activity_setting"
            android:exported="true" />
        <activity
            android:name=".ui.home.pixivision.PixivsionActivity"
            android:exported="true" />
        <activity
            android:name=".ui.home.pixivision.SpotlightActivity"
            android:exported="true" />
        <activity
            android:name=".ui.search.SearchActivity"
            android:label="@string/title_activity_search_r"
            android:exported="true" />
        <activity android:name=".ui.search.SearchResultActivity" />
        <activity
            android:name=".ui.pic.PictureActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".ui.settings.SaucenaoActivity"
            android:exported="true"
            android:label="@string/title_activity_saucenao">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="image/*" />
            </intent-filter>
        </activity>
        <activity
            android:name=".IntentActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="www.pixiv.net" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="pixiv.net" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="www.pixiv.net" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="pixiv.net" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="pixiv.me" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="pixiv.me" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="pixiv"/>
                <data android:host="users"/>
                <data android:host="illusts"/>
                <data android:host="account"/>
            </intent-filter>
        </activity>

        <receiver
            android:name=".objects.NewAppWidget"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/new_app_widget_info" />
        </receiver>
    </application>

</manifest>