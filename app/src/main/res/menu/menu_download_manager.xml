<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/action_settings"
        android:icon="@drawable/ic_settings"
        app:iconTint="?android:colorControlNormal"
        android:title="@string/task_setting"
        app:showAsAction ="ifRoom" />
    <item
        android:id="@+id/action_resume"
        android:title="@string/all_resume"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_finished_cancel"
        android:title="@string/finished_cancel"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_stop"
        android:title="@string/all_stop"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_cancel"
        android:title="@string/all_cancel"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_export"
        android:title="@string/action_export"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_import"
        android:title="@string/action_import"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_restart"
        android:title="@string/restart"
        app:showAsAction="never" />
</menu>