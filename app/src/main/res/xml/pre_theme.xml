<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <PreferenceCategory app:title="Color">
        <ListPreference
            android:entryValues="@array/dark_mode_value"
            app:defaultValue="-1"
            app:entries="@array/dark_mode"
            app:key="dark_mode"
            app:title="@string/dark_mode"
            app:useSimpleSummaryProvider="true" />
        <Preference
            app:key="theme"
            app:summary="TODO"
            app:title="@string/title_theme_color" />
    </PreferenceCategory>

    <PreferenceCategory app:title="Material">
        <SwitchPreferenceCompat
            android:defaultValue="true"
            android:key="material3"
            app:summary="Material You~"
            android:title="Material3" />
        <SwitchPreferenceCompat
            android:defaultValue="true"
            android:key="dynamicColor"
            app:summary="Material3 Only"
            android:title="@string/dynamic_color" />
        <SwitchPreferenceCompat
            android:defaultValue="true"
            android:key="harmonizeColor"
            android:title="@string/harmonize_color" />
    </PreferenceCategory>
    <PreferenceCategory app:title="UI">
        <SwitchPreferenceCompat
            android:summary="@string/use_bottom_appbar_subtext"
            app:defaultValue="true"
            app:key="bottomAppbar"
            app:title="@string/use_bottom_appbar" />
        <SwitchPreferenceCompat
            android:summary="@string/needstatusbar_subtext"
            app:key="needstatusbar"
            app:title="@string/needstatusbar" />
        <SwitchPreferenceCompat
            android:summary="@string/needactionbar_subtext"
            app:defaultValue="true"
            app:key="needactionbar"
            app:title="@string/needactionbar" />
        <SwitchPreferenceCompat
            app:key="refreshTab"
            app:defaultValue="false"
            app:title="@string/refreshTab"
            app:summary="@string/refreshTab_summary"/>
        <SwitchPreferenceCompat
            android:summary="@string/animation_summary"
            app:defaultValue="false"
            app:key="animation"
            app:title="@string/animation" />
        <SwitchPreferenceCompat
            android:summary="@string/banner_summary"
            app:defaultValue="false"
            app:key="banner_auto_loop"
            app:title="@string/banner_auto_loop" />
    </PreferenceCategory>
</androidx.preference.PreferenceScreen>
