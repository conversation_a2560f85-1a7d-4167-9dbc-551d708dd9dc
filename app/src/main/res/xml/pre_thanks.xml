<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <PreferenceCategory
        android:key="Support"
        android:title="Support">
        <Preference
            android:title="@string/supporttitle"
            app:key="support"
            app:summary="@string/supportsummary" />
        <Preference
            android:title="@string/PR"
            app:key="pr"
            app:summary="@string/welcomePR" />
        <Preference
            android:title="@string/thanks_to"
            app:key="thanks"
            app:summary="@string/thanks" />

    </PreferenceCategory>
    <PreferenceCategory app:title="Contributors">
        <Preference
            app:key="xuemo"
            app:icon="@drawable/xuemo"
            app:title="LuckXuemo"
            android:summary="💻" />
        <Preference
            android:summary="💻"
            app:icon="@drawable/ultranity"
            app:key="ultranity"
            app:title="ultranity" />
        <Preference
            app:key="hunterx9"
            app:icon="@drawable/hunterx9"
            app:title="hunterx9"
            android:summary="💻" />
        <Preference
            app:key="Skimige"
            app:title="Skimige"
            app:icon="@drawable/skimige"
            android:summary="📖" />
        <Preference
            app:key="TragicLife"
            app:title="Tragic Life"
            app:icon="@drawable/tragiclife"
            android:summary="🌍" />
        <Preference
            app:key="Misoni"
            app:title="Misoni"
            app:icon="@drawable/misoni"
            android:summary="🌍" />
    </PreferenceCategory>
    <PreferenceCategory
        android:key="huonaicai"
        android:title="@string/donate"
        app:isPreferenceVisible="false">
        <Preference
            android:title="@string/current_developer"
            app:icon="@drawable/ultranity"
            app:key="Ultranity"
            app:selectable="true"
            app:enableCopying="true"
            app:summary="@string/click" />
        <Preference
            android:title="原作者"
            app:icon="@drawable/xinobu"
            app:key="Notsfsssf"
            app:selectable="true"
            app:enableCopying="true"
            app:summary="@string/support_Notsfsssf" />
    </PreferenceCategory>
</androidx.preference.PreferenceScreen>