<resources>

    <declare-styleable name="ButtonBarContainerTheme">
        <attr name="metaButtonBarStyle" format="reference" />
        <attr name="metaButtonBarButtonStyle" format="reference" />
    </declare-styleable>
    <declare-styleable name="SwipeLayout">
        <attr name="drag_edge">
            <flag name="left" value="1" />
            <flag name="right" value="2" />
            <flag name="top" value="4" />
            <flag name="bottom" value="8" />
        </attr>
        <attr name="leftEdgeSwipeOffset" format="dimension" />
        <attr name="rightEdgeSwipeOffset" format="dimension" />
        <attr name="topEdgeSwipeOffset" format="dimension" />
        <attr name="bottomEdgeSwipeOffset" format="dimension" />
        <attr name="show_mode" format="enum">
            <enum name="lay_down" value="0" />
            <enum name="pull_out" value="1" />
        </attr>
        <attr name="clickToClose" format="boolean" />
    </declare-styleable>
    <declare-styleable name="RoundAngleFrameLayout">
        <attr name="radius" format="dimension" >25</attr>
        <attr name="topLeftRadius" format="dimension" />
        <attr name="topRightRadius" format="dimension" />
        <attr name="bottomLeftRadius" format="dimension" />
        <attr name="bottomRightRadius" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="NiceImageView">
        <attr name="is_circle" format="boolean" />
        <attr name="is_cover_src" format="boolean" />
        <attr name="corner_radius" format="dimension" />
        <attr name="corner_top_left_radius" format="dimension" />
        <attr name="corner_top_right_radius" format="dimension" />
        <attr name="corner_bottom_left_radius" format="dimension" />
        <attr name="corner_bottom_right_radius" format="dimension" />
        <attr name="border_width" format="dimension" />
        <attr name="border_color" format="color" />
        <attr name="inner_border_width" format="dimension" />
        <attr name="inner_border_color" format="color" />
        <attr name="mask_color" format="color" />
    </declare-styleable>
</resources>
