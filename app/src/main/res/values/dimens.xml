<resources>
    <dimen name="app_bar_height">180dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="text_margin">16dp</dimen>

    <!--
Refer to App Widget Documentation for margin information
http://developer.android.com/guide/topics/appwidgets/index.html#CreatingLayout
    -->
    <dimen name="widget_margin">0dp</dimen>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="nav_rail_width">80dp</dimen>
    <dimen name="fragment_horizontal_margin">16dp</dimen>
    <dimen name="item_transform_image_length">48dp</dimen>
    <dimen name="item_width">200dp</dimen>
    <dimen name="container_margin">8dp</dimen>
    <dimen name="container_horizontal_margin">16dp</dimen>
</resources>
