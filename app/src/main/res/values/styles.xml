<!--
  ~ MIT License
  ~
  ~ Copyright (c) 2019 Perol_Notsfsssf
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE
  -->

<resources>

    <style name="AppThemeBase3" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- enable window content transitions -->
        <item name="android:windowActivityTransitions">true</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">true</item>
        <item name="android:windowAllowReturnTransitionOverlap">true</item>
        <item name="android:windowSharedElementEnterTransition">@android:transition/move</item>
        <item name="android:windowSharedElementExitTransition">@android:transition/move</item>
        <item name="switchPreferenceCompatStyle">@style/Preference.SwitchPreferenceCompat.Catalog
        </item>
        <item name="android:windowAnimationStyle">@style/windowAnimationStyle</item>
    </style>

    <style name="AppThemeBase" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="badgeTextColor">@color/yellow</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="autoCompleteTextViewStyle">@style/AppAutoCompleteTextView</item>
        <!-- enable window content transitions -->
        <item name="android:windowActivityTransitions">true</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">true</item>
        <item name="android:windowAllowReturnTransitionOverlap">true</item>
        <item name="android:windowSharedElementEnterTransition">@android:transition/move</item>
        <item name="android:windowSharedElementExitTransition">@android:transition/move</item>
        <item name="android:windowAnimationStyle">@style/windowAnimationStyle</item>
    </style>

    <style name="windowAnimationStyle">
        　　<item name="android:activityOpenEnterAnimation">@anim/activity_enter</item>
        　　<item name="android:activityOpenExitAnimation">@anim/activity_hold</item>
        　　<item name="android:activityCloseEnterAnimation">@anim/activity_hold</item>
        　　<item name="android:activityCloseExitAnimation">@anim/activity_exit</item>
    </style>

    <style name="AppAutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="MyToolbar">
        <item name="colorControlNormal">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Preference.SwitchPreferenceCompat.Catalog" parent="Preference.SwitchPreferenceCompat.Material">
        <item name="android:widgetLayout">@layout/preference_widget_material_switch</item>
    </style>

    <style name="primary">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="blue">
        <item name="colorPrimary">@color/md_blue_300</item>
        <item name="colorPrimaryDark">@color/md_blue_400</item>
        <item name="colorAccent">@color/md_blue_400</item>
    </style>

    <style name="pink">
        <item name="colorPrimary">@color/pink</item>
        <item name="colorPrimaryDark">@color/pinkDark</item>
        <item name="colorAccent">@color/pink</item>
    </style>

    <style name="miku">
        <item name="colorPrimary">@color/miku</item>
        <item name="colorPrimaryDark">@color/mikuDark</item>
        <item name="colorAccent">@color/mikuDark</item>
    </style>

    <style name="purple">
        <item name="colorPrimary">@color/md_purple_300</item>
        <item name="colorPrimaryDark">@color/md_purple_500</item>
        <item name="colorAccent">@color/md_purple_500</item>
    </style>

    <style name="cyan">
        <item name="colorPrimary">@color/md_cyan_300</item>
        <item name="colorPrimaryDark">@color/md_cyan_500</item>
        <item name="colorAccent">@color/md_cyan_500</item>
    </style>

    <style name="green">
        <item name="colorPrimary">@color/md_green_300</item>
        <item name="colorPrimaryDark">@color/md_green_500</item>
        <item name="colorAccent">@color/md_green_500</item>
    </style>

    <style name="indigo">
        <item name="colorPrimary">@color/md_indigo_300</item>
        <item name="colorPrimaryDark">@color/md_indigo_500</item>
        <item name="colorAccent">@color/md_indigo_500</item>
    </style>

    <style name="now">
        <item name="colorPrimary">@color/now</item>
        <item name="colorPrimaryDark">@color/nowDark</item>
        <item name="colorAccent">@color/nowDark</item>
    </style>

    <style name="red">
        <item name="colorPrimary">@color/md_red_500</item>
        <item name="colorPrimaryDark">@color/md_red_600</item>
        <item name="colorAccent">@color/md_red_600</item>
    </style>

    <style name="primary1">
        <item name="android:colorPrimary">@color/md_pink_500</item>
        <item name="colorPrimary">@color/md_pink_500</item>
        <item name="colorPrimaryDark">@color/md_pink_700</item>
        <item name="colorAccent">@color/md_pink_500</item>
    </style>

    <style name="primary2">
        <item name="colorPrimary">@color/md_purple_500</item>
        <item name="colorPrimaryDark">@color/md_purple_700</item>
        <item name="colorAccent">@color/md_purple_500</item>
    </style>

    <style name="primary3">
        <item name="colorPrimary">@color/md_deep_purple_500</item>
        <item name="colorPrimaryDark">@color/md_deep_purple_700</item>
        <item name="colorAccent">@color/md_deep_purple_500</item>
    </style>

    <style name="primary4">
        <item name="colorPrimary">@color/md_indigo_500</item>
        <item name="colorPrimaryDark">@color/md_indigo_700</item>
        <item name="colorAccent">@color/md_indigo_500</item>
    </style>

    <style name="primary5">
        <item name="colorPrimary">@color/md_blue_500</item>
        <item name="colorPrimaryDark">@color/md_blue_700</item>
        <item name="colorAccent">@color/md_blue_500</item>
    </style>

    <style name="primary6">
        <item name="colorPrimary">@color/md_light_blue_500</item>
        <item name="colorPrimaryDark">@color/md_light_blue_700</item>
        <item name="colorAccent">@color/md_light_blue_500</item>
    </style>

    <style name="primary7">
        <item name="colorPrimary">@color/md_cyan_500</item>
        <item name="colorPrimaryDark">@color/md_cyan_700</item>
        <item name="colorAccent">@color/md_cyan_500</item>
    </style>

    <style name="primary8">
        <item name="colorPrimary">@color/md_teal_500</item>
        <item name="colorPrimaryDark">@color/md_teal_700</item>
        <item name="colorAccent">@color/md_teal_500</item>
    </style>

    <style name="primary9">
        <item name="colorPrimary">@color/md_green_500</item>
        <item name="colorPrimaryDark">@color/md_green_700</item>
        <item name="colorAccent">@color/md_green_500</item>
    </style>

    <style name="primary10">
        <item name="colorPrimary">@color/md_light_green_500</item>
        <item name="colorPrimaryDark">@color/md_light_green_700</item>
        <item name="colorAccent">@color/md_light_green_500</item>
    </style>

    <style name="primary11">
        <item name="colorPrimary">@color/md_lime_500</item>
        <item name="colorPrimaryDark">@color/md_lime_700</item>
        <item name="colorAccent">@color/md_lime_500</item>
    </style>

    <style name="primary12">
        <item name="colorPrimary">@color/md_yellow_500</item>
        <item name="colorPrimaryDark">@color/md_yellow_700</item>
        <item name="colorAccent">@color/md_yellow_500</item>
    </style>

    <style name="primary13">
        <item name="colorPrimary">@color/md_amber_500</item>
        <item name="colorPrimaryDark">@color/md_amber_700</item>
        <item name="colorAccent">@color/md_amber_500</item>
    </style>

    <style name="primary14">
        <item name="colorPrimary">@color/md_orange_500</item>
        <item name="colorPrimaryDark">@color/md_orange_700</item>
        <item name="colorAccent">@color/md_orange_500</item>
    </style>

    <style name="primary15">
        <item name="colorPrimary">@color/md_deep_orange_500</item>
        <item name="colorPrimaryDark">@color/md_deep_orange_700</item>
        <item name="colorAccent">@color/md_deep_orange_500</item>
    </style>

    <style name="primary16">
        <item name="colorPrimary">@color/md_brown_500</item>
        <item name="colorPrimaryDark">@color/md_brown_700</item>
        <item name="colorAccent">@color/md_brown_500</item>
    </style>

    <style name="primary17">
        <item name="colorPrimary">@color/md_grey_500</item>
        <item name="colorPrimaryDark">@color/md_grey_700</item>
        <item name="colorAccent">@color/md_grey_500</item>
    </style>

    <style name="primary18">
        <item name="colorPrimary">@color/md_blue_grey_500</item>
        <item name="colorPrimaryDark">@color/md_blue_grey_700</item>
        <item name="colorAccent">@color/md_blue_grey_500</item>
    </style>

    <style name="primary19">
        <item name="colorPrimary">@color/md_white_1000</item>
        <item name="colorPrimaryDark">@color/md_white_1000</item>
        <item name="colorAccent">@color/md_white_1000</item>
    </style>

    <style name="primary20">
        <item name="colorPrimary">@color/md_black_1000</item>
        <item name="colorPrimaryDark">@color/md_black_1000</item>
        <item name="colorAccent">@color/md_black_1000</item>
    </style>

</resources>
