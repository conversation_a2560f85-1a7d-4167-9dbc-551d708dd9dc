{"metadata": {"generated": "2023-07-29T06:24:01.659Z"}, "libraries": [{"uniqueId": "androidx.activity:activity", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.6.1", "description": "Provides the base Activity subclass and the relevant hooks to build a composable structure on top.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Activity", "website": "https://developer.android.com/jetpack/androidx/releases/activity#1.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.activity:activity-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.6.1", "description": "Kotlin extensions for 'activity' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Activity Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/activity#1.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.annotation:annotation-experimental", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.0", "description": "Java annotation for use on unstable Android API surfaces. When used in conjunction with the Experimental annotation lint checks, this annotation provides functional parity with <PERSON><PERSON><PERSON>'s Experimental annotation.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Experimental annotation", "website": "https://developer.android.com/jetpack/androidx/releases/annotation#1.3.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.annotation:annotation-jvm", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.6.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support Annotations", "website": "https://developer.android.com/jetpack/androidx/releases/annotation#1.6.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.appcompat:appcompat", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.6.1", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android AppCompat Library", "website": "https://developer.android.com/jetpack/androidx/releases/appcompat#1.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.appcompat:appcompat-resources", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.6.1", "description": "The Resources Library is a static library that you can add to your Android application in order to use resource APIs that backport the latest APIs to older versions of the platform. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Resources Library", "website": "https://developer.android.com/jetpack/androidx/releases/appcompat#1.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.arch.core:core-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.2.0", "description": "Android Arch-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Arch-Common", "website": "https://developer.android.com/jetpack/androidx/releases/arch-core#2.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.arch.core:core-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.2.0", "description": "Android Arch-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Arch-Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/arch-core#2.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.cardview:cardview", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "Android Support CardView v7", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support CardView v7", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.collection:collection", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Standalone efficient collections.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support collections", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.collection:collection-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Kotlin extensions for 'collection' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Collections Kotlin Extensions", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.concurrent:concurrent-futures", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Androidx implementation of Guava's ListenableFuture", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "AndroidX Futures", "website": "https://developer.android.com/topic/libraries/architecture/index.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.constraintlayout:constraintlayout", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.1.4", "description": "ConstraintLayout for Android", "scm": {"connection": "**************:androidx/constraintlayout.git", "url": "https://github.com/androidx/constraintlayout"}, "name": "Android ConstraintLayout", "website": "http://tools.android.com", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.constraintlayout:constraintlayout-core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.4", "description": "ConstraintLayout core", "scm": {"connection": "**************:androidx/constraintlayout.git", "url": "https://github.com/androidx/constraintlayout"}, "name": "Android ConstraintLayout Core", "website": "http://tools.android.com", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.coordinatorlayout:coordinatorlayout", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Coordinator Layout", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.core:core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.10.1", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support compat", "website": "https://developer.android.com/jetpack/androidx/releases/core#1.10.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.core:core-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.10.1", "description": "Kotlin extensions for 'core' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Core Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/core#1.10.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.cursoradapter:cursoradapter", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Cursor Adapter", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.customview:customview", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Custom View", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.customview:customview-poolingcontainer", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "Utilities for listening to the lifecycle of containers that manage their child Views' lifecycle, such as RecyclerView", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "androidx.customview:poolingcontainer", "website": "https://developer.android.com/jetpack/androidx/releases/customview#1.0.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.databinding:databinding-adapters", "funding": [], "developers": [], "artifactVersion": "7.1.3", "description": "", "name": "androidx.databinding:databinding-adapters", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.databinding:databinding-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "8.0.0", "description": "Shared library between Data Binding runtime lib and compiler", "name": "androidx.databinding.databinding-common", "website": "http://tools.android.com/", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.databinding:databinding-ktx", "funding": [], "developers": [], "artifactVersion": "7.1.3", "description": "", "name": "androidx.databinding:databinding-ktx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.databinding:databinding-runtime", "funding": [], "developers": [], "artifactVersion": "8.0.0", "description": "", "name": "androidx.databinding:databinding-runtime", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.databinding:viewbinding", "funding": [], "developers": [], "artifactVersion": "8.1.0", "description": "", "name": "androidx.databinding:viewbinding", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.documentfile:documentfile", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Document File", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.drawerlayout:drawerlayout", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Drawer Layout", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.dynamicanimation:dynamicanimation", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "Physics-based animation in support library, where the animations are driven by physics force. You can use this Animation library to create smooth and realistic animations.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support DynamicAnimation", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.emoji2:emoji2", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "Core library to enable emoji compatibility in Kitkat and newer devices to avoid the empty emoji characters.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Emoji2 Compat", "website": "https://developer.android.com/jetpack/androidx/releases/emoji2#1.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.emoji2:emoji2-views-helper", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "View helpers for Emoji2", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Emoji2 Compat view helpers", "website": "https://developer.android.com/jetpack/androidx/releases/emoji2#1.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.exifinterface:exifinterface", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.3", "description": "Android Support ExifInterface", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support ExifInterface", "website": "https://developer.android.com/jetpack/androidx/releases/exifinterface#1.3.3", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.fragment:fragment", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.6.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support fragment", "website": "https://developer.android.com/jetpack/androidx/releases/fragment#1.6.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.fragment:fragment-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.6.0", "description": "Kotlin extensions for 'fragment' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Fragment Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/fragment#1.6.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.interpolator:interpolator", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Interpolators", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.legacy:legacy-support-core-utils", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support core utils", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle-Common", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-common-java8", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle-Common for Java 8 Language", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle-Common for Java 8 Language", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-extensions", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.2.0", "description": "Android Lifecycle Extensions", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Android Lifecycle Extensions", "website": "https://developer.android.com/topic/libraries/architecture/index.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-livedata", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle LiveData", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle LiveData", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-livedata-core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle LiveData Core", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle LiveData Core", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-livedata-core-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Kotlin extensions for 'livedata-core' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "LiveData Core Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-livedata-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Kotlin extensions for 'livedata' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "LiveData Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-process", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle Process", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle Process", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Kotlin extensions for 'lifecycle' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-service", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle Service", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle Service", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle ViewModel", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Kotlin extensions for 'viewmodel' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle ViewModel Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-savedstate", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.1", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Lifecycle ViewModel with SavedState", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.6.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.loader:loader", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support loader", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.localbroadcastmanager:localbroadcastmanager", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Local Broadcast Manager", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.navigation:navigation-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.0", "description": "Android Navigation-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Navigation Common", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.6.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.navigation:navigation-fragment", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.0", "description": "Android Navigation-Fragment", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Navigation Fragment", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.6.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.navigation:navigation-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.6.0", "description": "Android Navigation-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Navigation Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.6.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.preference:preference", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "AndroidX Preference", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "AndroidX Preference", "website": "https://developer.android.com/jetpack/androidx/releases/preference#1.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.preference:preference-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "Kotlin extensions for preferences", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Preferences KTX", "website": "https://developer.android.com/jetpack/androidx/releases/preference#1.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.print:print", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Print", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.profileinstaller:profileinstaller", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.0", "description": "Allows libraries to prepopulate ahead of time compilation traces to be read by ART", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "androidx.profileinstaller:profileinstaller", "website": "https://developer.android.com/jetpack/androidx/releases/profileinstaller#1.3.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.recyclerview:recyclerview", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.1", "description": "Android Support RecyclerView", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support RecyclerView", "website": "https://developer.android.com/jetpack/androidx/releases/recyclerview#1.3.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.resourceinspection:resourceinspection-annotation", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.1", "description": "Annotation processors for Android resource and layout inspection", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Resource Inspection - Annotations", "website": "https://developer.android.com/jetpack/androidx/releases/resourceinspection#1.0.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.room:room-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.2", "description": "Android Room-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Room-Common", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.5.2", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.room:room-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.2", "description": "Android Room Kotlin Extensions", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Room Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.5.2", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.room:room-migration", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.2", "description": "Android Room Migration", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Room Migration", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.5.2", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.room:room-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.2", "description": "Android Room-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Room-Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.5.2", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.room:room-testing", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.2", "description": "Android Room Testing", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Room Testing", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.5.2", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.savedstate:savedstate", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.1", "description": "Android Lifecycle Saved State", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Saved State", "website": "https://developer.android.com/jetpack/androidx/releases/savedstate#1.2.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.savedstate:savedstate-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.1", "description": "Kotlin extensions for 'savedstate' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "SavedState Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/savedstate#1.2.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.slidingpanelayout:slidingpanelayout", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "SlidingPaneLayout offers a responsive, two pane layout that automatically switches between overlapping panes on smaller devices to a side by side view on larger devices.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support Sliding Pane Layout", "website": "https://developer.android.com/jetpack/androidx/releases/slidingpanelayout#1.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.sqlite:sqlite", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.3.1", "description": "Android DB", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android DB", "website": "https://developer.android.com/jetpack/androidx/releases/sqlite#2.3.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.sqlite:sqlite-framework", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.3.1", "description": "The implementation of Support SQLite library using the framework code.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support SQLite - Framework Implementation", "website": "https://developer.android.com/jetpack/androidx/releases/sqlite#2.3.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.startup:startup-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Android App Startup Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android App Startup Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/startup#1.1.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.swiperefreshlayout:swiperefreshlayout", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Custom View", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.tracing:tracing", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "Android Tracing", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Tracing", "website": "https://developer.android.com/jetpack/androidx/releases/tracing#1.0.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.transition:transition", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.4.1", "description": "Android Transition Support Library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Transition Support Library", "website": "https://developer.android.com/jetpack/androidx/releases/transition#1.4.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.vectordrawable:vectordrawable", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Android Support VectorDrawable", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support VectorDrawable", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.vectordrawable:vectordrawable-animated", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Android Support AnimatedVectorDrawable", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support AnimatedVectorDrawable", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.versionedparcelable:versionedparcelable", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Provides a stable but relatively compact binary serialization format that can be passed across processes or persisted safely.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "VersionedParcelable", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.viewpager2:viewpager2", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0-beta02", "description": "AndroidX Widget ViewPager2", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "ViewPager2", "website": "https://developer.android.com/jetpack/androidx/releases/viewpager2#1.1.0-beta02", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.viewpager:viewpager", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support View Pager", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.webkit:webkit", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.7.0", "description": "The WebView Support Library is a static library you can add to your Android application in order to use android.webkit APIs that are not available for older platform versions.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "WebView Support Library", "website": "https://developer.android.com/jetpack/androidx/releases/webkit#1.7.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.window:window", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "WindowManager Jetpack library. Currently only provides additional functionality on foldable devices.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Jetpack WindowManager Library", "website": "https://developer.android.com/jetpack/androidx/releases/window#1.0.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.afollestad.material-dialogs:bottomsheets", "funding": [], "developers": [], "artifactVersion": "3.3.0", "description": "", "name": "com.afollestad.material-dialogs:bottomsheets", "licenses": []}, {"uniqueId": "com.afollestad.material-dialogs:core", "funding": [], "developers": [], "artifactVersion": "3.3.0", "description": "", "name": "com.afollestad.material-dialogs:core", "licenses": []}, {"uniqueId": "com.afollestad.material-dialogs:files", "funding": [], "developers": [], "artifactVersion": "3.3.0", "description": "", "name": "com.afollestad.material-dialogs:files", "licenses": []}, {"uniqueId": "com.afollestad.material-dialogs:input", "funding": [], "developers": [], "artifactVersion": "3.3.0", "description": "", "name": "com.afollestad.material-dialogs:input", "licenses": []}, {"uniqueId": "com.afollestad.material-dialogs:lifecycle", "funding": [], "developers": [], "artifactVersion": "3.3.0", "description": "", "name": "com.afollestad.material-dialogs:lifecycle", "licenses": []}, {"uniqueId": "com.afollestad:drag-select-recyclerview", "funding": [], "developers": [], "artifactVersion": "2.4.0", "description": "", "name": "com.afollestad:drag-select-recyclerview", "licenses": []}, {"uniqueId": "com.arialyy.aria:annotations", "funding": [], "developers": [], "artifactVersion": "3.8.12", "description": "", "name": "com.arialyy.aria:annotations", "licenses": []}, {"uniqueId": "com.arialyy.aria:core", "funding": [], "developers": [], "artifactVersion": "3.8.12", "description": "", "name": "com.arialyy.aria:core", "licenses": []}, {"uniqueId": "com.arialyy.aria:httpComponent", "funding": [], "developers": [], "artifactVersion": "3.8.12", "description": "", "name": "com.arialyy.aria:httpComponent", "licenses": []}, {"uniqueId": "com.arialyy.aria:publicComponent", "funding": [], "developers": [], "artifactVersion": "3.8.12", "description": "", "name": "com.arialyy.aria:publicComponent", "licenses": []}, {"uniqueId": "com.atlassian.commonmark:commonmark", "funding": [], "developers": [], "artifactVersion": "0.13.0", "description": "Core of commonmark-java (implementation of CommonMark for parsing markdown and rendering to HTML)", "scm": {"connection": "scm:git:**************:atlassian/commonmark-java.git", "url": "https://github.com/atlassian/commonmark-java", "developerConnection": "scm:git:**************:atlassian/commonmark-java.git"}, "name": "commonmark-java core", "website": "https://github.com/atlassian/commonmark-java", "licenses": ["BSD-2-<PERSON><PERSON>"], "organization": {"url": "https://www.atlassian.com/", "name": "Atlassian"}}, {"uniqueId": "com.chrynan.parcelable:parcelable-core-android", "funding": [], "developers": [], "artifactVersion": "0.7.1", "description": "", "name": "com.chrynan.parcelable:parcelable-core-android", "licenses": []}, {"uniqueId": "com.chrynan.parcelable:parcelable-core-android-debug", "funding": [], "developers": [], "artifactVersion": "0.7.1", "description": "", "name": "com.chrynan.parcelable:parcelable-core-android-debug", "licenses": []}, {"uniqueId": "com.davemorrissey.labs:subsampling-scale-image-view-androidx", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "3.10.0", "description": "Highly configurable, easily extendable deep zoom view for displaying huge images without loss of detail. Perfect for photo galleries, maps, building plans etc.", "scm": {"connection": "scm:**************:davemorrissey/subsampling-scale-image-view.git", "url": "scm:**************:davemorrissey/subsampling-scale-image-view.git", "developerConnection": "scm:**************:davemorrissey/subsampling-scale-image-view.git"}, "name": "SubsamplingScaleImageView", "website": "https://github.com/davemorrissey/subsampling-scale-image-view", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.github.Dhaval2404:ColorPicker", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "2.3", "description": "A ColorPicker library for android.", "scm": {"connection": "https://github.com/Dhaval2404/ColorPicker.git", "url": "https://github.com/Dhaval2404/ColorPicker/", "developerConnection": "https://github.com/Dhaval2404/ColorPicker.git"}, "name": "colorpicker", "website": "https://github.com/Dhaval2404/ColorPicker/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.github.beksomega:loopinglayout", "funding": [], "developers": [{"name": "Beka <PERSON>berg"}], "artifactVersion": "0.5.0", "description": "A library that adds a looping layout manager for recycler views.", "scm": {"connection": "scm:git:github.com/BeksOmega/looping-layout.git", "url": "https://github.com/getstream/stream-chat-android/tree/master", "developerConnection": "scm:git:ssh://github.com/BeksOmega/looping-layout.git"}, "name": "loopinglayout", "website": "https://github.com/BeksOmega/looping-layout", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.github.bumptech.glide:annotations", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "4.15.1", "description": "A set of annotations for configuring Glide.", "scm": {"connection": "scm:**************:bumptech/glide.git", "url": "https://github.com/bumptech/glide", "developerConnection": "scm:**************:bumptech/glide.git"}, "name": "Glide Annotations", "website": "https://github.com/bumptech/glide", "licenses": ["Apache-2.0", "BSD-2-<PERSON><PERSON>"]}, {"uniqueId": "com.github.bumptech.glide:disklrucache", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "4.15.1", "description": "A cache that uses a bounded amount of space on a filesystem. Based on <PERSON>'s tailored for Glide.", "scm": {"connection": "scm:**************:bumptech/glide.git", "url": "https://github.com/bumptech/glide", "developerConnection": "scm:**************:bumptech/glide.git"}, "name": "Glide Disk LRU Cache Library", "website": "https://github.com/bumptech/glide", "licenses": ["Apache-2.0", "BSD-2-<PERSON><PERSON>"]}, {"uniqueId": "com.github.bumptech.glide:gifdecoder", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "4.15.1", "description": "Implementation of GifDecoder that is more memory efficient to animate for Android devices.", "scm": {"connection": "scm:**************:bumptech/glide.git", "url": "https://github.com/bumptech/glide", "developerConnection": "scm:**************:bumptech/glide.git"}, "name": "Glide GIF Decoder Library", "website": "https://github.com/bumptech/glide", "licenses": ["Apache-2.0", "BSD-2-<PERSON><PERSON>"]}, {"uniqueId": "com.github.bumptech.glide:glide", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "4.15.1", "description": "A fast and efficient image loading library for Android focused on smooth scrolling.", "scm": {"connection": "scm:**************:bumptech/glide.git", "url": "https://github.com/bumptech/glide", "developerConnection": "scm:**************:bumptech/glide.git"}, "name": "Glide", "website": "https://github.com/bumptech/glide", "licenses": ["Apache-2.0", "BSD-2-<PERSON><PERSON>"]}, {"uniqueId": "com.github.bumptech.glide:okhttp3-integration", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "4.15.1", "description": "An integration library to use OkHttp 3.x to fetch data over http/https in Glide", "scm": {"connection": "scm:**************:bumptech/glide.git", "url": "https://github.com/bumptech/glide", "developerConnection": "scm:**************:bumptech/glide.git"}, "name": "Glide OkHttp 3.x Integration", "website": "https://github.com/bumptech/glide", "licenses": ["Apache-2.0", "BSD-2-<PERSON><PERSON>"]}, {"uniqueId": "com.github.liangjingkanji:BRV", "funding": [], "developers": [], "artifactVersion": "1.4.3", "description": "", "name": "com.github.liangjingkanji:BRV", "licenses": []}, {"uniqueId": "com.github.liangjingkanji:StateLayout", "funding": [], "developers": [], "artifactVersion": "1.4.1", "description": "", "name": "com.github.liangjingkanji:StateLayout", "licenses": []}, {"uniqueId": "com.google.android.flexbox:flexbox", "funding": [], "developers": [{"name": "Google"}], "artifactVersion": "3.0.0", "description": "Flexbox for Android", "scm": {"connection": "https://github.com/google/flexbox-layout.git", "url": "https://github.com/google/flexbox-layout"}, "name": "flexbox-layout", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.google.android.material:material", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.9.0", "description": "Material Components for Android is a static library that you can add to your Android application in order to use APIs that provide implementations of the Material Design specification. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://github.com/material-components/material-components-android.git", "url": "https://github.com/material-components/material-components-android"}, "name": "Material Components for Android", "website": "https://github.com/material-components/material-components-android", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.google.code.gson:gson", "funding": [], "developers": [], "artifactVersion": "2.8.0", "description": "Gson JSON library", "scm": {"connection": "scm:git:https://github.com/google/gson.git", "url": "https://github.com/google/gson/", "developerConnection": "scm:git:**************:google/gson.git"}, "name": "Gson", "website": "https://github.com/google/gson", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.google.errorprone:error_prone_annotations", "funding": [], "developers": [], "artifactVersion": "2.15.0", "description": "Error Prone is a static analysis tool for Java that catches common programming mistakes at compile-time.", "scm": {"connection": "scm:git:https://github.com/google/error-prone.git", "url": "https://github.com/google/error-prone", "developerConnection": "scm:git:**************:google/error-prone.git"}, "name": "error-prone annotations", "website": "https://errorprone.info", "licenses": ["Apache-2.0"], "organization": {"url": "http://www.google.com", "name": "Google LLC"}}, {"uniqueId": "com.google.guava:listenablefuture", "funding": [], "developers": [], "artifactVersion": "1.0", "description": "Contains Guava's com.google.common.util.concurrent.ListenableFuture class,\n    without any of its other classes -- but is also available in a second\n    \"version\" that omits the class to avoid conflicts with the copy in Guava\n    itself. The idea is:\n\n    - If users want only ListenableFuture, they depend on listenablefuture-1.0.\n\n    - If users want all of Guava, they depend on guava, which, as of Guava\n    27.0, depends on\n    listenablefuture-9999.0-empty-to-avoid-conflict-with-guava. The 9999.0-...\n    version number is enough for some build systems (notably, Gradle) to select\n    that empty artifact over the \"real\" listenablefuture-1.0 -- avoiding a\n    conflict with the copy of ListenableFuture in guava itself. If users are\n    using an older version of Guava or a build system other than Gradle, they\n    may see class conflicts. If so, they can solve them by manually excluding\n    the listenablefuture artifact or manually forcing their build systems to\n    use 9999.0-....", "scm": {"connection": "scm:git:https://github.com/google/guava.git", "url": "https://github.com/google/guava", "developerConnection": "scm:git:**************:google/guava.git"}, "name": "Guava ListenableFuture only", "website": "https://github.com/google/guava", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.hyman:flowlayout-lib", "funding": [], "developers": [], "artifactVersion": "1.1.2", "description": "", "name": "com.hyman:flowlayout-lib", "licenses": []}, {"uniqueId": "com.mikepenz:aboutlibraries-core-android", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "10.8.3", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:aboutlibraries-core-android-debug", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "10.8.3", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:fastadapter", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "5.7.0", "description": "The bullet proof, fast and easy to use adapter library, which minimizes developing time to a fraction...", "scm": {"connection": "scm:**************:mikepenz/FastAdapter.git", "url": "https://github.com/mikepenz/FastAdapter", "developerConnection": "scm:**************:mikepenz/FastAdapter.git"}, "name": "FastAdapter Library", "website": "https://github.com/mikepenz/FastAdapter", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okhttp3:logging-interceptor", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "4.11.0", "description": "Square’s meticulous HTTP client for Java and Kotlin.", "scm": {"connection": "scm:git:https://github.com/square/okhttp.git", "url": "https://github.com/square/okhttp", "developerConnection": "scm:git:ssh://**************/square/okhttp.git"}, "name": "okhttp-logging-interceptor", "website": "https://square.github.io/okhttp/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okhttp3:okhttp", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "4.11.0", "description": "Square’s meticulous HTTP client for Java and Kotlin.", "scm": {"connection": "scm:git:https://github.com/square/okhttp.git", "url": "https://github.com/square/okhttp", "developerConnection": "scm:git:ssh://**************/square/okhttp.git"}, "name": "okhttp", "website": "https://square.github.io/okhttp/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okhttp3:okhttp-dnsoverhttps", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "4.11.0", "description": "Square’s meticulous HTTP client for Java and Kotlin.", "scm": {"connection": "scm:git:https://github.com/square/okhttp.git", "url": "https://github.com/square/okhttp", "developerConnection": "scm:git:ssh://**************/square/okhttp.git"}, "name": "okhttp-dnsoverhttps", "website": "https://square.github.io/okhttp/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okio:okio", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "2.4.3", "description": "A modern I/O API for Java", "scm": {"connection": "scm:git:git://github.com/square/okio.git", "url": "https://github.com/square/okio/", "developerConnection": "scm:git:ssh://**************/square/okio.git"}, "name": "<PERSON><PERSON>", "website": "https://github.com/square/okio/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.retrofit2:retrofit", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "2.9.0", "description": "A type-safe HTTP client for Android and Java.", "scm": {"connection": "scm:git:git://github.com/square/retrofit.git", "url": "https://github.com/square/retrofit/", "developerConnection": "scm:git:ssh://**************/square/retrofit.git"}, "name": "Retrofit", "website": "https://github.com/square/retrofit", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.tencent:mmkv-static", "funding": [], "developers": [{"name": "Tencent Wechat, Inc."}], "artifactVersion": "1.3.0", "description": "MMKV for Android", "scm": {"connection": "scm:git:github.com/Tencent/MMKV.git", "url": "https://github.com/Tencent/MMKV", "developerConnection": "scm:git:ssh://**************/Tencent/MMKV.git"}, "name": "MMKV", "website": "https://github.com/Tencent/MMKV", "licenses": ["BSD-3-<PERSON><PERSON>"]}, {"uniqueId": "com.waynejo:and<PERSON>ndkgif", "funding": [], "developers": [], "artifactVersion": "0.3.3", "description": "", "name": "com.waynejo:and<PERSON>ndkgif", "licenses": []}, {"uniqueId": "io.github.cymchad:BaseRecyclerViewAdapterHelper", "funding": [], "developers": [{"name": "l<PERSON><PERSON><PERSON>"}], "artifactVersion": "3.0.14", "description": "Powerful and flexible RecyclerAdapter", "scm": {"connection": "scm:**************/CymChad/BaseRecyclerViewAdapterHelper.git", "url": "https://github.com/CymChad/BaseRecyclerViewAdapterHelper", "developerConnection": "scm:**************/CymChad/BaseRecyclerViewAdapterHelper.git"}, "name": "BaseRecyclerViewAdapterHelper", "website": "https://github.com/CymChad/BaseRecyclerViewAdapterHelper", "licenses": ["MIT"]}, {"uniqueId": "io.github.knight-zxw:blockcanary", "funding": [], "developers": [{"name": "Knight-ZXW"}], "artifactVersion": "0.0.5", "description": "blocking detecting libary for Android", "scm": {"connection": "scm:git:git://github.com/Knight-ZXW/BlockCanaryX.git", "url": "https://github.com/Knight-ZXW/BlockCanaryX", "developerConnection": "scm:git:ssh://**************/Knight-ZXW/BlockCanaryX.git"}, "name": "blockcanary", "website": "https://github.com/Knight-ZXW/BlockCanaryX/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.github.knight-zxw:blockcanary-ui", "funding": [], "developers": [{"name": "Knight-ZXW"}], "artifactVersion": "0.0.5", "description": "blocking detecting libary for Android", "scm": {"connection": "scm:git:git://github.com/Knight-ZXW/BlockCanaryX.git", "url": "https://github.com/Knight-ZXW/BlockCanaryX", "developerConnection": "scm:git:ssh://**************/Knight-ZXW/BlockCanaryX.git"}, "name": "blockcanary-ui", "website": "https://github.com/Knight-ZXW/BlockCanaryX/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.github.knight-zxw:flamegraph-ui", "funding": [], "developers": [{"name": "Knight-ZXW"}], "artifactVersion": "0.0.5", "description": "blocking detecting libary for Android", "scm": {"connection": "scm:git:git://github.com/Knight-ZXW/BlockCanaryX.git", "url": "https://github.com/Knight-ZXW/BlockCanaryX", "developerConnection": "scm:git:ssh://**************/Knight-ZXW/BlockCanaryX.git"}, "name": "flamegraph-ui", "website": "https://github.com/Knight-ZXW/BlockCanaryX/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.github.knight-zxw:stacksampler", "funding": [], "developers": [{"name": "Knight-ZXW"}], "artifactVersion": "0.0.5", "description": "blocking detecting libary for Android", "scm": {"connection": "scm:git:git://github.com/Knight-ZXW/BlockCanaryX.git", "url": "https://github.com/Knight-ZXW/BlockCanaryX", "developerConnection": "scm:git:ssh://**************/Knight-ZXW/BlockCanaryX.git"}, "name": "StackSampler", "website": "https://github.com/Knight-ZXW/BlockCanaryX/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.github.scwang90:refresh-drawable-paint", "funding": [], "developers": [{"name": "scwang90"}], "artifactVersion": "2.0.5", "description": "common drawables for refresh animations", "scm": {"connection": "scm:git:github.com/scwang90/SmartRefreshLayout.git", "url": "https://github.com/scwang90/SmartRefreshLayout", "developerConnection": "scm:git:ssh://**************/scwang90/SmartRefreshLayout.git"}, "name": "refresh-drawable-paint", "website": "https://github.com/scwang90/SmartRefreshLayout", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.github.scwang90:refresh-footer-classics", "funding": [], "developers": [{"name": "scwang90"}], "artifactVersion": "2.0.5", "description": "the footer <PERSON><PERSON><PERSON>er of SmartRefreshLayout", "scm": {"connection": "scm:git:github.com/scwang90/SmartRefreshLayout.git", "url": "https://github.com/scwang90/SmartRefreshLayout", "developerConnection": "scm:git:ssh://**************/scwang90/SmartRefreshLayout.git"}, "name": "refresh-footer-classics", "website": "https://github.com/scwang90/SmartRefreshLayout", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.github.scwang90:refresh-header-material", "funding": [], "developers": [{"name": "scwang90"}], "artifactVersion": "2.0.5", "description": "the header MaterialHeader of SmartRefreshLayout", "scm": {"connection": "scm:git:github.com/scwang90/SmartRefreshLayout.git", "url": "https://github.com/scwang90/SmartRefreshLayout", "developerConnection": "scm:git:ssh://**************/scwang90/SmartRefreshLayout.git"}, "name": "refresh-header-material", "website": "https://github.com/scwang90/SmartRefreshLayout", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.github.scwang90:refresh-layout-kernel", "funding": [], "developers": [{"name": "scwang90"}], "artifactVersion": "2.0.5", "description": "the kernel of SmartRefreshLayout", "scm": {"connection": "scm:git:github.com/scwang90/SmartRefreshLayout.git", "url": "https://github.com/scwang90/SmartRefreshLayout", "developerConnection": "scm:git:ssh://**************/scwang90/SmartRefreshLayout.git"}, "name": "refresh-layout-kernel", "website": "https://github.com/scwang90/SmartRefreshLayout", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.noties.mark<PERSON>:core", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.6.2", "description": "Core Markwon artifact that includes basic markdown parsing and rendering", "scm": {"connection": "scm:git:git://github.com/noties/Markwon.git", "url": "https://github.com/noties/<PERSON>won", "developerConnection": "scm:git:git://github.com/noties/Markwon.git"}, "name": "Core", "website": "https://github.com/noties/<PERSON>won", "licenses": ["Apache-2.0"]}, {"uniqueId": "jp.was<PERSON><PERSON>:glide-transformations", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.3.0", "description": "Which provides simple Transformations to Glide", "scm": {"connection": "https://github.com/wasabeef/glide-transformations.git", "url": "https://github.com/wasabeef/glide-transformations", "developerConnection": "https://github.com/wasabeef/glide-transformations.git"}, "name": "glide-transformations", "website": "https://github.com/wasabeef/glide-transformations", "licenses": ["Apache-2.0"]}, {"uniqueId": "junit:junit", "funding": [], "developers": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "artifactVersion": "4.13.2", "description": "JUnit is a unit testing framework for Java, created by <PERSON> and <PERSON>.", "scm": {"connection": "scm:git:git://github.com/junit-team/junit4.git", "url": "https://github.com/junit-team/junit4", "developerConnection": "scm:git:**************:junit-team/junit4.git"}, "name": "JUnit", "website": "http://junit.org", "licenses": ["EPL-1.0"], "organization": {"url": "http://www.junit.org", "name": "JUnit"}}, {"uniqueId": "net.lingala.zip4j:zip4j", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "2.11.5", "description": "Zip4j - A Java library for zip files and streams", "scm": {"url": "**************:srikanth-lingala/zip4j.git"}, "name": "Zip4j", "website": "https://github.com/srikanth-lingala/zip4j", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.hamcrest:hamcrest-core", "funding": [], "developers": [], "artifactVersion": "1.3", "description": "This is the core API of hamcrest matcher framework to be used by third-party framework providers. This includes the a foundation set of matcher implementations for common operations.", "scm": {"connection": "scm:git:**************:hamcrest/JavaHamcrest.git", "url": "https://github.com/hamcrest/JavaHamcrest"}, "name": "Hamcrest Core", "website": "https://github.com/hamcrest/JavaHamcrest", "licenses": ["BSD-3-<PERSON><PERSON>"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-android-extensions-runtime", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.8.0", "description": "Kotlin Android Extensions Runtime", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Kotlin Android Extensions Runtime", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-parcelize-runtime", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.8.0", "description": "Runtime library for the Parcelize compiler plugin", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Parcelize Runtime", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.9.0", "description": "Kotlin Standard Library for JVM", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "<PERSON><PERSON><PERSON>", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib-common", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.9.0", "description": "Kotlin Common Standard Library", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "<PERSON><PERSON><PERSON>", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib-jdk7", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.8.22", "description": "Kotlin Standard Library JDK 7 extension", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Kotlin Stdlib Jdk7", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib-jdk8", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.8.22", "description": "Kotlin Standard Library JDK 8 extension", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Kotlin Stdlib Jdk8", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-coroutines-android", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.7.2", "description": "Coroutines support libraries for Kotlin", "scm": {"url": "https://github.com/Kotlin/kotlinx.coroutines"}, "name": "kotlinx-coroutines-android", "website": "https://github.com/Kotlin/kotlinx.coroutines", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-coroutines-bom", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.7.2", "description": "Coroutines support libraries for Kotlin", "scm": {"url": "https://github.com/Kotlin/kotlinx.coroutines"}, "name": "kotlinx-coroutines-bom", "website": "https://github.com/Kotlin/kotlinx.coroutines", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.7.2", "description": "Coroutines support libraries for Kotlin", "scm": {"url": "https://github.com/Kotlin/kotlinx.coroutines"}, "name": "kotlinx-coroutines-core", "website": "https://github.com/Kotlin/kotlinx.coroutines", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-bom", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.5.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-bom", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-core-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.5.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-core", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-json-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.5.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-json", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains:annotations", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "23.0.0", "description": "A set of annotations used for code inspection support and code documentation.", "scm": {"connection": "scm:git:git://github.com/JetBrains/java-annotations.git", "url": "https://github.com/JetBrains/java-annotations", "developerConnection": "scm:git:ssh://github.com:JetBrains/java-annotations.git"}, "name": "JetBrains Java Annotations", "website": "https://github.com/JetBrains/java-annotations", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jsoup:jsoup", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "1.16.1", "description": "jsoup is a Java library for working with real-world HTML. It provides a very convenient API for fetching URLs and extracting and manipulating data, using the best of HTML5 DOM methods and CSS selectors. jsoup implements the WHATWG HTML5 specification, and parses HTML to the same DOM as modern browsers do.", "scm": {"connection": "scm:git:https://github.com/jhy/jsoup.git", "url": "https://github.com/jhy/jsoup"}, "name": "jsoup Java HTML Parser", "website": "https://jsoup.org/", "licenses": ["MIT"], "organization": {"url": "https://jhy.io/", "name": "<PERSON>"}}, {"uniqueId": "org.reactivestreams:reactive-streams", "funding": [], "developers": [{"name": "Reactive Streams SIG"}], "artifactVersion": "1.0.3", "description": "A Protocol for Asynchronous Non-Blocking Data Sequence", "scm": {"connection": "scm:git:**************:reactive-streams/reactive-streams.git", "url": "**************:reactive-streams/reactive-streams.git"}, "name": "reactive-streams", "website": "http://www.reactive-streams.org/", "licenses": ["CC0-1.0"]}, {"uniqueId": "org.roaringbitmap:RoaringBitmap", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "0.9.47", "description": "Roaring bitmaps are compressed bitmaps (also called bitsets) which tend to outperform conventional compressed bitmaps such as WAH or Concise.", "scm": {"connection": "scm:git:https://github.com/RoaringBitmap/RoaringBitmap.git", "url": "https://github.com/RoaringBitmap/RoaringBitmap", "developerConnection": "scm:git:https://github.com/RoaringBitmap/RoaringBitmap.git"}, "name": "org.roaringbitmap:RoaringBitmap", "website": "https://github.com/RoaringBitmap/RoaringBitmap", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.roaringbitmap:shims", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "0.9.45", "description": "Roaring bitmaps are compressed bitmaps (also called bitsets) which tend to outperform conventional compressed bitmaps such as WAH or Concise.", "scm": {"connection": "scm:git:https://github.com/RoaringBitmap/RoaringBitmap.git", "url": "https://github.com/RoaringBitmap/RoaringBitmap", "developerConnection": "scm:git:https://github.com/RoaringBitmap/RoaringBitmap.git"}, "name": "org.roaringbitmap:shims", "website": "https://github.com/RoaringBitmap/RoaringBitmap", "licenses": ["Apache-2.0"]}], "licenses": {"Apache-2.0": {"content": "Apache License\nVersion 2.0, January 2004\nhttp://www.apache.org/licenses/\n\nTERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n1. Definitions.\n\n\"License\" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.\n\n\"Licensor\" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.\n\n\"Legal Entity\" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, \"control\" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.\n\n\"You\" (or \"Your\") shall mean an individual or Legal Entity exercising permissions granted by this License.\n\n\"Source\" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.\n\n\"Object\" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.\n\n\"Work\" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).\n\n\"Derivative Works\" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.\n\n\"Contribution\" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, \"submitted\" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as \"Not a Contribution.\"\n\n\"Contributor\" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.\n\n2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.\n\n3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.\n\n4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:\n\n     (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and\n\n     (b) You must cause any modified files to carry prominent notices stating that You changed the files; and\n\n     (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and\n\n     (d) If the Work includes a \"NOTICE\" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.\n\n     You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.\n\n5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.\n\n6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.\n\n7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.\n\n8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.\n\n9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability.\n\nEND OF TERMS AND CONDITIONS\n\nAPPENDIX: How to apply the Apache License to your work.\n\nTo apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets \"[]\" replaced with your own identifying information. (Don't include the brackets!)  The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same \"printed page\" as the copyright notice for easier identification within third-party archives.\n\nCopyright [yyyy] [name of copyright owner]\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\nhttp://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.", "hash": "Apache-2.0", "internalHash": "Apache-2.0", "url": "apache_2_0", "spdxId": "Apache-2.0", "name": "Apache License 2.0"}, "BSD-2-Clause": {"content": "Copyright (c) <<var;name=copyright;original= <year> <owner>;match=.+>> All rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer. \n\n2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY <<var;name=copyrightHolderAsIs;original=THE COPYRIGHT HOLDERS AND CONTRIBUTORS;match=.+>> \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF <PERSON><PERSON><PERSON><PERSON>ABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL <<var;name=copyrightHolderLiability;original=THE COPYRIGHT HOLDER OR CONTRIBUTORS;match=.+>> BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIA<PERSON>, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.", "hash": "BSD-2-<PERSON><PERSON>", "internalHash": "BSD-2-<PERSON><PERSON>", "url": "https://spdx.org/licenses/BSD-2-Clause.html", "spdxId": "BSD-2-<PERSON><PERSON>", "name": "BSD 2-Clause \"Simplified\" License"}, "BSD-3-Clause": {"content": "Copyright (c) <<var;name=copyright;original= <year> <owner>;match=.+>>. All rights reserved. \n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer. \n\n2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution. \n\n3. Neither the name of <<var;name=organizationClause3;original=the copyright holder;match=.+>> nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY <<var;name=copyrightHolderAsIs;original=THE COPYRIGHT HOLDERS AND CONTRIBUTORS;match=.+>> \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL <<var;name=copyrightHolderLiability;original=THE COPYRIGHT HOLDER OR CONTRIBUTORS;match=.+>> BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. ", "hash": "BSD-3-<PERSON><PERSON>", "internalHash": "BSD-3-<PERSON><PERSON>", "url": "https://spdx.org/licenses/BSD-3-Clause.html", "spdxId": "BSD-3-<PERSON><PERSON>", "name": "BSD 3-Clause \"New\" or \"Revised\" License"}, "CC0-1.0": {"content": "Creative Commons CC0 1.0 Universal\n\n<<beginOptional;name=ccOptionalIntro>> CREATIVE COMMONS CORPORATION IS NOT A LAW FIRM AND DOES NOT PROVIDE LEGAL SERVICES. DISTRIBUTION OF THIS DOCUMENT DOES NOT CREATE AN ATTORNEY-CLIENT RELATIONSHIP. CREATIVE COMMONS PROVIDES THIS INFORMATION ON AN \"AS-IS\" BASIS. CREATIVE COMMONS MAKES NO WARRANTIES REGARDING THE USE OF THIS DOCUMENT OR THE INFORMATION OR WORKS PROVIDED HEREUNDER, AND DISCLAIMS LIABILITY FOR DAMAGES RESULTING FROM THE USE OF THIS DOCUMENT OR THE INFORMATION OR WORKS PROVIDED HEREUNDER.  <<endOptional>>\n\nStatement of Purpose\n\nThe laws of most jurisdictions throughout the world automatically confer exclusive Copyright and Related Rights (defined below) upon the creator and subsequent owner(s) (each and all, an \"owner\") of an original work of authorship and/or a database (each, a \"Work\").\n\nCertain owners wish to permanently relinquish those rights to a Work for the purpose of contributing to a commons of creative, cultural and scientific works (\"Commons\") that the public can reliably and without fear of later claims of infringement build upon, modify, incorporate in other works, reuse and redistribute as freely as possible in any form whatsoever and for any purposes, including without limitation commercial purposes. These owners may contribute to the Commons to promote the ideal of a free culture and the further production of creative, cultural and scientific works, or to gain reputation or greater distribution for their Work in part through the use and efforts of others.\n\nFor these and/or other purposes and motivations, and without any expectation of additional consideration or compensation, the person associating CC0 with a Work (the \"Affirmer\"), to the extent that he or she is an owner of Copyright and Related Rights in the Work, voluntarily elects to apply CC0 to the Work and publicly distribute the Work under its terms, with knowledge of his or her Copyright and Related Rights in the Work and the meaning and intended legal effect of CC0 on those rights.\n\n1. Copyright and Related Rights. A Work made available under CC0 may be protected by copyright and related or neighboring rights (\"Copyright and Related Rights\"). Copyright and Related Rights include, but are not limited to, the following:\n\n     i. the right to reproduce, adapt, distribute, perform, display, communicate, and translate a Work;\n\n     ii. moral rights retained by the original author(s) and/or performer(s);\n\n     iii. publicity and privacy rights pertaining to a person's image or likeness depicted in a Work;\n\n     iv. rights protecting against unfair competition in regards to a Work, subject to the limitations in paragraph 4(a), below;\n\n     v. rights protecting the extraction, dissemination, use and reuse of data in a Work;\n\n     vi. database rights (such as those arising under Directive 96/9/EC of the European Parliament and of the Council of 11 March 1996 on the legal protection of databases, and under any national implementation thereof, including any amended or successor version of such directive); and\n\n     vii. other similar, equivalent or corresponding rights throughout the world based on applicable law or treaty, and any national implementations thereof.\n\n2. Waiver. To the greatest extent permitted by, but not in contravention of, applicable law, Affirmer hereby overtly, fully, permanently, irrevocably and unconditionally waives, abandons, and surrenders all of Affirmer's Copyright and Related Rights and associated claims and causes of action, whether now known or unknown (including existing as well as future claims and causes of action), in the Work (i) in all territories worldwide, (ii) for the maximum duration provided by applicable law or treaty (including future time extensions), (iii) in any current or future medium and for any number of copies, and (iv) for any purpose whatsoever, including without limitation commercial, advertising or promotional purposes (the \"Waiver\"). Affirmer makes the Waiver for the benefit of each member of the public at large and to the detriment of Affirmer's heirs and successors, fully intending that such Waiver shall not be subject to revocation, rescission, cancellation, termination, or any other legal or equitable action to disrupt the quiet enjoyment of the Work by the public as contemplated by Affirmer's express Statement of Purpose.\n\n3. Public License Fallback. Should any part of the Waiver for any reason be judged legally invalid or ineffective under applicable law, then the Waiver shall be preserved to the maximum extent permitted taking into account Affirmer's express Statement of Purpose. In addition, to the extent the Waiver is so judged Affirmer hereby grants to each affected person a royalty-free, non transferable, non sublicensable, non exclusive, irrevocable and unconditional license to exercise Affirmer's Copyright and Related Rights in the Work (i) in all territories worldwide, (ii) for the maximum duration provided by applicable law or treaty (including future time extensions), (iii) in any current or future medium and for any number of copies, and (iv) for any purpose whatsoever, including without limitation commercial, advertising or promotional purposes (the \"License\"). The License shall be deemed effective as of the date CC0 was applied by Affirmer to the Work. Should any part of the License for any reason be judged legally invalid or ineffective under applicable law, such partial invalidity or ineffectiveness shall not invalidate the remainder of the License, and in such case Affirmer hereby affirms that he or she will not (i) exercise any of his or her remaining Copyright and Related Rights in the Work or (ii) assert any associated claims and causes of action with respect to the Work, in either case contrary to Affirmer's express Statement of Purpose.\n\n4. Limitations and Disclaimers.\n\n     a. No trademark or patent rights held by Affirmer are waived, abandoned, surrendered, licensed or otherwise affected by this document.\n\n     b. Affirmer offers the Work as-is and makes no representations or warranties of any kind concerning the Work, express, implied, statutory or otherwise, including without limitation warranties of title, merchantability, fitness for a particular purpose, non infringement, or the absence of latent or other defects, accuracy, or the present or absence of errors, whether or not discoverable, all to the greatest extent permissible under applicable law.\n\n     c. Affirmer disclaims responsibility for clearing rights of other persons that may apply to the Work or any use thereof, including without limitation any person's Copyright and Related Rights in the Work. Further, Affirmer disclaims responsibility for obtaining any necessary consents, permissions or other rights required for any use of the Work.\n\n     d. Affirmer understands and acknowledges that Creative Commons is not a party to this document and has no duty or obligation with respect to this CC0 or use of the Work. ", "hash": "CC0-1.0", "internalHash": "CC0-1.0", "url": "cc0_1_0", "spdxId": "CC0-1.0", "name": "Creative Commons Zero v1.0 Universal"}, "EPL-1.0": {"content": "Eclipse Public License - v 1.0\n\nTHE ACCOMPANYING PROGRAM IS PROVIDED UNDER THE TERMS OF THIS ECLIPSE PUBLIC LICENSE (\"AGREEMENT\"). ANY USE, REPRODUCTION OR DISTRIBUTION OF THE PROGRAM CONSTITUTES RECIPIENT'S ACCEPTANCE OF THIS AGREEMENT.\n\n1. DEFINITIONS\n\n\"Contribution\" means:\n     a) in the case of the initial Contributor, the initial code and documentation distributed under this Agreement, and\n     b) in the case of each subsequent Contributor:\n          i) changes to the Program, and\n          ii) additions to the Program;\n\nwhere such changes and/or additions to the Program originate from and are distributed by that particular Contributor. A Contribution 'originates' from a Contributor if it was added to the Program by such Contributor itself or anyone acting on such Contributor's behalf. Contributions do not include additions to the Program which: (i) are separate modules of software distributed in conjunction with the Program under their own license agreement, and (ii) are not derivative works of the Program.\n\"Contributor\" means any person or entity that distributes the Program.\n\n\"Licensed Patents\" mean patent claims licensable by a Contributor which are necessarily infringed by the use or sale of its Contribution alone or when combined with the Program.\n\n\"Program\" means the Contributions distributed in accordance with this Agreement.\n\n\"Recipient\" means anyone who receives the Program under this Agreement, including all Contributors.\n\n2. GRANT OF RIGHTS\n\n     a) Subject to the terms of this Agreement, each Contributor hereby grants Recipient a non-exclusive, worldwide, royalty-free copyright license to reproduce, prepare derivative works of, publicly display, publicly perform, distribute and sublicense the Contribution of such Contributor, if any, and such derivative works, in source code and object code form.\n \n     b) Subject to the terms of this Agreement, each Contributor hereby grants Recipient a non-exclusive, worldwide, royalty-free patent license under Licensed Patents to make, use, sell, offer to sell, import and otherwise transfer the Contribution of such Contributor, if any, in source code and object code form. This patent license shall apply to the combination of the Contribution and the Program if, at the time the Contribution is added by the Contributor, such addition of the Contribution causes such combination to be covered by the Licensed Patents. The patent license shall not apply to any other combinations which include the Contribution. No hardware per se is licensed hereunder.\n\n     c) Recipient understands that although each Contributor grants the licenses to its Contributions set forth herein, no assurances are provided by any Contributor that the Program does not infringe the patent or other intellectual property rights of any other entity. Each Contributor disclaims any liability to Recipient for claims brought by any other entity based on infringement of intellectual property rights or otherwise. As a condition to exercising the rights and licenses granted hereunder, each Recipient hereby assumes sole responsibility to secure any other intellectual property rights needed, if any. For example, if a third party patent license is required to allow Recipient to distribute the Program, it is Recipient's responsibility to acquire that license before distributing the Program.\n\n     d) Each Contributor represents that to its knowledge it has sufficient copyright rights in its Contribution, if any, to grant the copyright license set forth in this Agreement.\n\n3. REQUIREMENTS\nA Contributor may choose to distribute the Program in object code form under its own license agreement, provided that:\n\n     a) it complies with the terms and conditions of this Agreement; and\n \n     b) its license agreement:\n          i) effectively disclaims on behalf of all Contributors all warranties and conditions, express and implied, including warranties or conditions of title and non-infringement, and implied warranties or conditions of merchantability and fitness for a particular purpose;\n          ii) effectively excludes on behalf of all Contributors all liability for damages, including direct, indirect, special, incidental and consequential damages, such as lost profits;\n          iii) states that any provisions which differ from this Agreement are offered by that Contributor alone and not by any other party; and\n          iv) states that source code for the Program is available from such Contributor, and informs licensees how to obtain it in a reasonable manner on or through a medium customarily used for software exchange.\n\nWhen the Program is made available in source code form:\n\n     a) it must be made available under this Agreement; and\n\n     b) a copy of this Agreement must be included with each copy of the Program.\nContributors may not remove or alter any copyright notices contained within the Program.\n\nEach Contributor must identify itself as the originator of its Contribution, if any, in a manner that reasonably allows subsequent Recipients to identify the originator of the Contribution.\n\n4. COMMERCIAL DISTRIBUTION\nCommercial distributors of software may accept certain responsibilities with respect to end users, business partners and the like. While this license is intended to facilitate the commercial use of the Program, the Contributor who includes the Program in a commercial product offering should do so in a manner which does not create potential liability for other Contributors. Therefore, if a Contributor includes the Program in a commercial product offering, such Contributor (\"Commercial Contributor\") hereby agrees to defend and indemnify every other Contributor (\"Indemnified Contributor\") against any losses, damages and costs (collectively \"Losses\") arising from claims, lawsuits and other legal actions brought by a third party against the Indemnified Contributor to the extent caused by the acts or omissions of such Commercial Contributor in connection with its distribution of the Program in a commercial product offering. The obligations in this section do not apply to any claims or Losses relating to any actual or alleged intellectual property infringement. In order to qualify, an Indemnified Contributor must: a) promptly notify the Commercial Contributor in writing of such claim, and b) allow the Commercial Contributor to control, and cooperate with the Commercial Contributor in, the defense and any related settlement negotiations. The Indemnified Contributor may participate in any such claim at its own expense.\n\nFor example, a Contributor might include the Program in a commercial product offering, Product X. That Contributor is then a Commercial Contributor. If that Commercial Contributor then makes performance claims, or offers warranties related to Product X, those performance claims and warranties are such Commercial Contributor's responsibility alone. Under this section, the Commercial Contributor would have to defend claims against the other Contributors related to those performance claims and warranties, and if a court requires any other Contributor to pay any damages as a result, the Commercial Contributor must pay those damages.\n\n5. NO WARRANTY\nEXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, THE PROGRAM IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE. Each Recipient is solely responsible for determining the appropriateness of using and distributing the Program and assumes all risks associated with its exercise of rights under this Agreement , including but not limited to the risks and costs of program errors, compliance with applicable laws, damage to or loss of data, programs or equipment, and unavailability or interruption of operations.\n\n6. DISCLAIMER OF LIABILITY\nEXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, NEITHER RECIPIENT NOR ANY CONTRIBUTORS SHALL HAVE ANY LIABILITY FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING WITHOUT LIMITATION LOST PROFITS), HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OR DISTRIBUTION OF THE PROGRAM OR THE EXERCISE OF ANY RIGHTS GRANTED HEREUNDER, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.\n\n7. GENERAL\n\nIf any provision of this Agreement is invalid or unenforceable under applicable law, it shall not affect the validity or enforceability of the remainder of the terms of this Agreement, and without further action by the parties hereto, such provision shall be reformed to the minimum extent necessary to make such provision valid and enforceable.\n\nIf Recipient institutes patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Program itself (excluding combinations of the Program with other software or hardware) infringes such Recipient's patent(s), then such Recipient's rights granted under Section 2(b) shall terminate as of the date such litigation is filed.\n\nAll Recipient's rights under this Agreement shall terminate if it fails to comply with any of the material terms or conditions of this Agreement and does not cure such failure in a reasonable period of time after becoming aware of such noncompliance. If all Recipient's rights under this Agreement terminate, Recipient agrees to cease use and distribution of the Program as soon as reasonably practicable. However, Recipient's obligations under this Agreement and any licenses granted by Recipient relating to the Program shall continue and survive.\n\nEveryone is permitted to copy and distribute copies of this Agreement, but in order to avoid inconsistency the Agreement is copyrighted and may only be modified in the following manner. The Agreement Steward reserves the right to publish new versions (including revisions) of this Agreement from time to time. No one other than the Agreement Steward has the right to modify this Agreement. The Eclipse Foundation is the initial Agreement Steward. The Eclipse Foundation may assign the responsibility to serve as the Agreement Steward to a suitable separate entity. Each new version of the Agreement will be given a distinguishing version number. The Program (including Contributions) may always be distributed subject to the version of the Agreement under which it was received. In addition, after a new version of the Agreement is published, Contributor may elect to distribute the Program (including its Contributions) under the new version. Except as expressly stated in Sections 2(a) and 2(b) above, Recipient receives no rights or licenses to the intellectual property of any Contributor under this Agreement, whether expressly, by implication, estoppel or otherwise. All rights in the Program not expressly granted under this Agreement are reserved.\n\nThis Agreement is governed by the laws of the State of New York and the intellectual property laws of the United States of America. No party to this Agreement will bring a legal action under this Agreement more than one year after the cause of action arose. Each party waives its rights to a jury trial in any resulting litigation.", "hash": "EPL-1.0", "internalHash": "EPL-1.0", "url": "https://spdx.org/licenses/EPL-1.0.html", "spdxId": "EPL-1.0", "name": "Eclipse Public License 1.0"}, "MIT": {"content": "MIT License\n\nCopyright (c) <year> <copyright holders>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.", "hash": "MIT", "internalHash": "MIT", "url": "mit", "spdxId": "MIT", "name": "MIT License"}}}