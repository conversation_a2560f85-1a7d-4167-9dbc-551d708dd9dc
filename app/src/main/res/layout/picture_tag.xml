<?xml version="1.0" encoding="utf-8"?>
<com.google.android.flexbox.FlexboxLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="8dp"
    android:orientation="horizontal"
    app:alignContent="stretch"
    app:alignItems="stretch"
    app:flexWrap="wrap">

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ac"
        android:textColor="?attr/colorPrimary" />

    <TextView
        android:id="@+id/translated_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone" />
</com.google.android.flexbox.FlexboxLayout>


