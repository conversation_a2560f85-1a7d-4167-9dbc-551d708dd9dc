<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/textview_userhead"
        android:src="@mipmap/ic_noimage"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="150dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:text="@string/followings" />
    <TextView
        android:id="@+id/textview_guanzhuzhe"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        tools:text="363"
        android:textSize="20sp"
        android:textColor="@color/colorPrimaryDark"/>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:text="@string/goodpfriend" />
    <TextView
        android:id="@+id/textview_haoPyou"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        tools:text="363"
        android:textSize="20sp"
        android:textColor="@color/colorPrimaryDark"/>
</LinearLayout>

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:text="@string/followers" />

    <TextView
        android:id="@+id/textview_hold"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:text="363"
        android:textColor="@color/colorPrimaryDark"
        android:textSize="20sp" />
</LinearLayout>
    </LinearLayout>


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="210dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/textview_userbody"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_marginStart="16dp"
            tools:text="@string/support_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>