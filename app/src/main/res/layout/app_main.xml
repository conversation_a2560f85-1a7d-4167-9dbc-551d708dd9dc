<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:fitsSystemWindows="false"
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/nav_toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/colorPrimary"
                android:minHeight="?attr/actionBarSize"
                android:theme="@style/MyToolbar"
                app:contentInsetEndWithActions="0dp"
                app:contentInsetStart="0.0dp"
                app:contentInsetStartWithNavigation="0dp"
                app:layout_scrollFlags="scroll|snap|enterAlways">
                <!--include
                    android:id="@+id/tablayout"
                    layout="@layout/app_main_tabs"/-->
                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tablayout"
                    style="@style/Widget.MaterialComponents.TabLayout.Colored"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:tabGravity="center"
                    app:tabIndicatorHeight="0dp"
                    app:tabMode="scrollable"
                    app:tabPadding="0dp"
                    app:tabPaddingStart="20dp">

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:icon="@drawable/ic_action_home" />

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:icon="@drawable/ic_action_rank" />

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:icon="@drawable/ic_action_user" />
                </com.google.android.material.tabs.TabLayout>
            </com.google.android.material.appbar.MaterialToolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Container of the nav rail or nav drawer. -->
        <LinearLayout
            android:id="@+id/nav_container"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <!-- Navigation drawer to be used for large screens. -->
            <com.google.android.material.navigation.NavigationView
                android:id="@+id/nav_drawer"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:visibility="gone"
                app:headerLayout="@layout/nav_header_rail"
                app:menu="@menu/nav_drawer" />

            <!-- Navigation rail to be used for medium screens. -->
            <com.google.android.material.navigationrail.NavigationRailView
                android:id="@+id/nav_rail"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:fitsSystemWindows="false"
                android:visibility="gone"
                app:headerLayout="@layout/nav_header_rail"
                app:labelVisibilityMode="unlabeled"
                app:menu="@menu/nav_drawer"
                app:menuGravity="center" />

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/content_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>

        <com.google.android.material.bottomappbar.BottomAppBar
            android:visibility="gone"
            style="@style/Widget.MaterialComponents.BottomAppBar.Colored"
            android:id="@+id/bottom_toolbar"
            android:layout_gravity="bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="?attr/colorPrimary"
            android:minHeight="?attr/actionBarSize"
            android:theme="@style/MyToolbar"
            android:paddingTop="0dp"
            app:contentInsetEndWithActions="0dp"
            app:contentInsetStart="0.0dp"
            app:contentInsetStartWithNavigation="0dp"
            app:menuAlignmentMode="auto"
            app:hideOnScroll="true">

            <ViewStub
                android:layout_height="match_parent"
                android:layout_width="match_parent"
                android:id="@+id/bottom_tabs" />
        </com.google.android.material.bottomappbar.BottomAppBar>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/main_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>
