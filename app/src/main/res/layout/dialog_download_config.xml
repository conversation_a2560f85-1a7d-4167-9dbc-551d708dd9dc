<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingStart="20dp"
    android:paddingTop="40dp"
    android:paddingEnd="20dp"
    android:paddingBottom="40dp">

    <TextView
        android:id="@+id/textView1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:text="@string/max_task_num" />

    <Spinner
        android:id="@+id/spinner_max_task_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:layout_gravity="end"
        android:defaultValue="2"
        android:entries="@array/thread_num_entries"
        android:entryValues="@array/thread_num_entries"
        android:spinnerMode="dialog" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider" />

    <TextView
        android:id="@+id/textView2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:text="@string/thread_num" />

    <Spinner
        android:id="@+id/spinner_thread_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:layout_gravity="end"
        android:defaultValue="2"
        android:entries="@array/thread_num_entries"
        android:entryValues="@array/thread_num_entries"
        android:spinnerMode="dialog" />
</LinearLayout>