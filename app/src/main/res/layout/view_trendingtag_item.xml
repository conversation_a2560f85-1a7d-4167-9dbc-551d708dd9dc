<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:paddingStart="8dp"
    android:paddingTop="8dp"
    android:paddingEnd="8dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/imageview_trendingtag"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="top"
            android:adjustViewBounds="true"
            android:scaleType="fitStart" />

        <TextView
            android:id="@+id/textview_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:fontFamily="monospace"
            android:textAlignment="center"
            android:textColor="?attr/colorPrimary"
            android:textDirection="anyRtl"
            android:textSize="20sp" />
    </FrameLayout>

</com.google.android.material.card.MaterialCardView>