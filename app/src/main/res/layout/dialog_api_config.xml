<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout  xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/dns_proxy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/dns_proxy" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/shuffle_IP"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/dns_proxy"
        android:text="@string/ip_shuffle" />

    <Button
        android:id="@+id/refreshDNS"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@+id/shuffle_IP"
        android:text="@string/refreshDNS" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/dns_proxy"
        android:layout_marginStart="4dp"
        android:layout_marginBottom="8dp"
        android:id="@+id/ip"
        android:text="@string/ip" />

    <EditText
        android:id="@+id/ipInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/dns_proxy"
        android:layout_toEndOf="@+id/ip"
        android:hint="@string/i_pximg_net_ip_list"
        android:importantForAutofill="no"
        android:inputType="textUri|textNoSuggestions" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/force_IP"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ipInput"
        android:text="@string/ip_download" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/api_mirror"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ipInput"
        android:layout_toEndOf="@id/force_IP"
        android:text="@string/api_mirror" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/api_mirror"
        android:layout_marginBottom="8dp"
        android:id="@+id/text"
        android:text="@string/url" />

    <EditText
        android:id="@+id/urlInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/api_mirror"
        android:layout_toEndOf="@+id/text"
        android:hint="@string/i_pximg_net"
        android:importantForAutofill="no"
        android:inputType="textUri" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/urlInput"
        android:layout_marginBottom="8dp"
        android:id="@+id/text2"
        android:text="@string/format" />

    <EditText
        android:id="@+id/formatInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/urlInput"
        android:layout_toEndOf="@+id/text2"
        android:hint="{host}/{params}"
        android:inputType="text"
        tools:ignore="HardcodedText"
        android:importantForAutofill="no" />

    <TableLayout
        android:id="@+id/format_desc_table"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/formatInput"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/table_bg">

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="2dp"
            android:showDividers="middle">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="@string/customformat_token"
                android:textColor="?android:textColorPrimary"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:layout_marginTop="1dp"
                android:text="@string/customformat_result"
                android:textColor="?android:textColorPrimary"
                android:textSize="16sp" />
        </TableRow>


        <TableRow
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="?attr/selectableItemBackground"
            android:padding="2dp"
            android:tag="{host}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="{host}"
                android:textColor="?android:textColorSecondary" />

            <TextView
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/mirror_link_hint"
                android:textColor="?android:textColorSecondary" />
        </TableRow>


        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:padding="2dp"
            android:tag="{params}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="{params}"
                android:textColor="?android:textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="@string/params_desc"
                android:textColor="?android:textColorSecondary" />
        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:padding="2dp"
            android:tag="{time}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="{time}"
                android:textColor="?android:textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="@string/create_time"
                android:textColor="?android:textColorSecondary" />
        </TableRow>


        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:padding="2dp"
            android:tag="{illustid}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="{illustid}"
                android:textColor="?android:textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="@string/Illust_id_desc"
                android:textColor="?android:textColorSecondary" />
        </TableRow>


        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:padding="2dp"
            android:tag="{part}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="{part}"
                android:textColor="?android:textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="@string/Which_part_desc"
                android:textColor="?android:textColorSecondary" />
        </TableRow>


        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:padding="2dp"
            android:tag="{type}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="{type}"
                android:textColor="?android:textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:text="@string/type_desc"
                android:textColor="?android:textColorSecondary" />
        </TableRow>
    </TableLayout>

</RelativeLayout>