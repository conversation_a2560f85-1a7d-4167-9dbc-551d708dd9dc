<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.perol.asdpl.pixivez.ui.pic.PictureActivity">


    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpage_picture"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        android:minHeight="?attr/actionBarSize" />


</FrameLayout>
