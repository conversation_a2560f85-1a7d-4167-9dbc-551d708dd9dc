<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.user.UserMActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:fitsSystemWindows="true"
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="220dp"
            android:background="?attr/colorSurface"
            app:contentScrim="?attr/colorPrimary"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:titleEnabled="false"
            app:toolbarId="@+id/toolbar">

            <ImageView
                android:id="@+id/imageview_user_background"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_bar_height"
                android:adjustViewBounds="true"
                android:fitsSystemWindows="true"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax"
                app:layout_collapseParallaxMultiplier="0.7"
                android:contentDescription="@string/background"
                tools:src="@drawable/background_with_shadow" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginBottom="40dp"
                app:layout_scrollFlags="scroll">

                <ImageView
                    android:id="@+id/imageview_userimage"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="8dp"
                    app:cardCornerRadius="40dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="@string/avatar"
                    tools:src="@drawable/ultranity" />

                <TextView
                    android:id="@+id/textview_username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignStart="@id/imageview_userimage"
                    android:layout_alignParentTop="true"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="8dp"
                    android:gravity="center"
                    tools:text="@string/username"
                    app:layout_constraintBottom_toBottomOf="@+id/imageview_userimage"
                    app:layout_constraintStart_toEndOf="@+id/imageview_userimage" />

                <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
                    android:id="@+id/fab"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|top"
                    android:layout_marginHorizontal="40dp"
                    android:text="@string/follow"
                    android:visibility="gone"
                    app:icon="@drawable/ic_add_white_24dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="@+id/textview_username"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:theme="@style/MyToolbar"
                app:layout_collapseMode="pin" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tablayout"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_gravity="bottom|center_horizontal"
                android:background="#00000000"
                app:layout_scrollFlags="exitUntilCollapsed"
                app:tabGravity="center"
                app:tabMode="scrollable" />
        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>