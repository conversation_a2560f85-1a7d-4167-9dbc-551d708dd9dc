<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/img_btn_config"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:layout_weight="1.5"
        app:icon="@drawable/ic_settings"
        app:iconGravity="textStart"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/img_btn_r"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:layout_weight="1"
        android:text="TAG"
        app:icon="@drawable/ic_action_tag"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</LinearLayout>
