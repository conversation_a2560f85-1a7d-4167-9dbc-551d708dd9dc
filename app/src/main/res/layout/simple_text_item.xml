<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardUseCompatPadding="true"
    app:cardPreventCornerOverlap="true">

    <TextView
        tools:text="1"
        android:id="@+id/text"
        android:textSize="16sp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:layout_width="match_parent"
        android:gravity="center_vertical"
        android:layout_height="wrap_content" />
</com.google.android.material.card.MaterialCardView>
