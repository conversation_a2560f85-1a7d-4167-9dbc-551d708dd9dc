<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="bottom"
    android:id="@+id/constraintLayout"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_comments"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        app:layout_constraintBottom_toTopOf="@+id/edittext_comment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <!--androidx.constraintlayout.helper.widget.Layer
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        app:constraint_referenced_ids="button,edittext_comment"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/button"
        app:layout_constraintStart_toStartOf="@+id/edittext_comment"
        app:layout_constraintTop_toBottomOf="@+id/recyclerview_comments" /-->

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/edittext_comment"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/button" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:clickable="false"
        android:text="@string/post_comment"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>