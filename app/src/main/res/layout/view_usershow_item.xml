<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="400dp"
    android:layout_height="wrap_content"
    android:id="@+id/cardview"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="4dp"
    app:cardElevation="5dp"
    app:cardMaxElevation="5dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.perol.asdpl.pixivez.view.NiceImageView
            android:id="@+id/imageview_usershow"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            app:border_color='?attr/colorPrimary'
            app:border_width="3.3dp"
            app:is_circle="true"
            android:hapticFeedbackEnabled="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview_usershow"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageview_usershow"
            app:layout_constraintTop_toBottomOf="@+id/textview_usershowname"
            app:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/textview_usershowname"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"

            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:text="千夜SQ"
            android:textColor="?attr/colorPrimary"
            android:textSize="20sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageview_usershow"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--<LinearLayout-->
    <!--android:layout_gravity="bottom"-->
    <!--android:layout_width="match_parent"-->
    <!--android:background="#80FFFFFF"-->
    <!--android:layout_height="50dp"/>-->


</com.google.android.material.card.MaterialCardView>