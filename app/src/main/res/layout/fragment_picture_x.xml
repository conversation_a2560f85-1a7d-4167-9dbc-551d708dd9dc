<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/frameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraint_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:visibility="gone"
                android:id="@+id/recyclerview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?android:attr/colorBackground" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraint_layout_fold"
                android:layout_width="match_parent"
                android:layout_height="68dp"
                android:visibility="invisible"
                tools:visibility="visible"
                android:background="?android:attr/colorBackground"
                android:layout_gravity="bottom"
                app:layout_constraintBottom_toBottomOf="parent">

                <ImageButton
                    android:id="@+id/imageButton"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:background="?android:attr/colorBackground"
                    android:src="@drawable/ic_action_fold"
                    android:contentDescription="@string/go_to_detail"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <com.perol.asdpl.pixivez.view.NiceImageView
                    android:id="@+id/imageview_user_picX"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    app:border_color='?attr/colorPrimary'
                    app:border_width="4dp"
                    app:is_circle="true"
                    android:hapticFeedbackEnabled="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/imageButton"
                    app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:ellipsize="end"
                android:lines="1"
                tools:text="{illust.title}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageview_user_picX"
                app:layout_constraintTop_toTopOf="@+id/imageview_user_picX" />

                <TextView
                    android:id="@+id/textView_user_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:ellipsize="end"
                    android:lines="1"
                    tools:text="{illust.user.name}"
                    android:layout_toEndOf="@id/imageview_user_picX"
                    android:textColor="?attr/colorPrimary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/imageview_user_picX"
                    app:layout_constraintTop_toBottomOf="@+id/textView_title" />

            <TextView
                android:id="@+id/textView_illust_create_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                tools:text="{illust.create_date}"
                android:textColor="@android:color/darker_gray"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageview_user_picX"
                app:layout_constraintTop_toBottomOf="@+id/textView_user_name" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/progress_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />

        </FrameLayout>

        <LinearLayout
            android:id="@+id/block_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/colorSurface"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:text="@string/block_face"
                android:textSize="24sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/block_tag" />

            <TextView
                android:id="@+id/blocktag_textview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/blocktag_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/jump_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/setting" />
        </LinearLayout>
    </FrameLayout>


    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/fab_margin"
        app:backgroundTint="?attr/colorBackgroundFloating"
        app:layout_anchor="@+id/frameLayout"
        android:visibility="gone"
        app:layout_anchorGravity="end|bottom"
        app:tint="@null"/>


</androidx.coordinatorlayout.widget.CoordinatorLayout>
