<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.perol.asdpl.pixivez.ui.account.NewUserActivity">

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.appcompat.widget.LinearLayoutCompat
        style="@style/Widget.AppCompat.ButtonBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/ext"
            style="@style/Widget.AppCompat.Button.ButtonBar.AlertDialog"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/open_in_external_browser" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</LinearLayout>
