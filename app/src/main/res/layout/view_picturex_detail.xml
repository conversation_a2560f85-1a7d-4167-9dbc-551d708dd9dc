<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearLayout_detail"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.perol.asdpl.pixivez.view.NiceImageView
            android:id="@+id/imageview_user_picX"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginHorizontal="4dp"
            app:border_color='?attr/colorPrimary'
            app:border_width="4dp"
            android:hapticFeedbackEnabled="true"
            app:is_circle="true" />

        <TextView
            android:id="@+id/textView_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_toEndOf="@id/imageview_user_picX"
            tools:text="{illust.title}"
            android:transitionName="title"
            android:textIsSelectable="true"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textView_user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/textView_title"
            android:layout_toEndOf="@id/imageview_user_picX"
            tools:text="{illust.user.name}"
            android:textColor="?attr/colorPrimary"
            android:textIsSelectable="true"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textView_illust_create_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/textView_user_name"
            android:layout_toEndOf="@id/imageview_user_picX"
            tools:text="{illust.create_date}"
            android:textSize="12sp" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/picX_detail_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/textview11"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:text="@string/illustid"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textview_illust_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            tools:text="1111111111"
            android:textColor="?attr/colorPrimary"
            android:textIsSelectable="true"
            app:layout_constraintStart_toEndOf="@+id/textview11"
            app:layout_constraintTop_toTopOf="@+id/textview11" />

        <TextView
            android:id="@+id/textView29"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"

            android:text="@string/pixel"
            app:layout_constraintStart_toEndOf="@+id/textview_illust_id"
            app:layout_constraintTop_toTopOf="@+id/textview11" />

        <TextView
            android:id="@+id/pixelWxH"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            tools:text='1024x768'
            android:textColor="?attr/colorPrimary"
            android:textIsSelectable="true"
            app:layout_constraintStart_toEndOf="@+id/textView29"
            app:layout_constraintTop_toTopOf="@+id/textView29" />

        <TextView
            android:id="@+id/AI_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="AI"
            android:visibility="invisible"
            tools:visibility="visible"
            app:layout_constraintStart_toEndOf="@+id/pixelWxH"
            app:layout_constraintTop_toTopOf="@+id/pixelWxH" />

        <TextView
            android:id="@+id/AI_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            tools:text='half'
            android:textColor="?attr/colorPrimary"
            android:textIsSelectable="false"
            app:layout_constraintStart_toEndOf="@+id/AI_text"
            app:layout_constraintTop_toTopOf="@+id/AI_text" />

        <TextView
            android:id="@+id/textView_total_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:text="@string/view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textview11" />

        <TextView
            android:id="@+id/total_view_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            tools:text="110"
            android:textColor="?attr/colorPrimary"
            android:textIsSelectable="true"
            app:layout_constraintStart_toEndOf="@+id/textView_total_view"
            app:layout_constraintTop_toTopOf="@+id/textView_total_view" />

        <TextView
            android:id="@+id/textview_bookmarked"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="@string/bookmark"
            app:layout_constraintStart_toEndOf="@id/total_view_num"
            app:layout_constraintTop_toTopOf="@+id/total_view_num" />

        <TextView
            android:id="@+id/bookmarked_user_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            tools:text="220"
            android:textColor="?attr/colorPrimary"
            app:layout_constraintStart_toEndOf="@+id/textview_bookmarked"
            app:layout_constraintTop_toTopOf="@+id/textview_bookmarked" />

        <TextView
            android:id="@+id/textview_san"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="san"
            app:layout_constraintStart_toEndOf="@+id/bookmarked_user_num"
            app:layout_constraintTop_toTopOf="@+id/bookmarked_user_num" />

        <TextView
            android:id="@+id/san"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            tools:text='7'
            android:textColor="?attr/colorPrimary"
            android:textIsSelectable="false"
            app:layout_constraintStart_toEndOf="@+id/textview_san"
            app:layout_constraintTop_toTopOf="@+id/textview_san" />

        <ImageButton
            android:id="@+id/imagebutton_share"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_action_share"
            app:layout_constraintEnd_toStartOf="@+id/imagebutton_download"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="?android:attr/textColorPrimary"
            android:contentDescription="@string/share" />
        <ImageButton
            android:id="@+id/imagebutton_download"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_action_download"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="?android:attr/textColorPrimary"
            android:contentDescription="@string/download" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/tag_group"
        app:chipSpacing="1dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="4dp" />

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/textview_caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:linksClickable="true"
            android:textIsSelectable="true"
            tools:text="@string/app_help" />

    </com.google.android.material.card.MaterialCardView>

    <FrameLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:paddingStart="4dp"
        android:paddingEnd="4dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_view_related"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/related"
            android:layout_gravity="start" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_view_comment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/view_comment"
            android:layout_gravity="center_horizontal" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_translate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/translate"
            android:layout_gravity="end" />
    </FrameLayout>
</LinearLayout>