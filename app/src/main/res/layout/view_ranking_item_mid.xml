<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardview"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginTop="4dp"
    android:layout_marginEnd="2dp"
    app:cardElevation="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/item_img"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:maxHeight="720dp"
                android:src="?attr/colorSurface"
                android:transitionName="mainimage"
                />

            <TextView
                android:id="@+id/textview_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="2dp"
                android:layout_gravity="end"
                android:alpha="0.3"
                android:background="#e0000000"
                tools:text="11"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="20sp"/>
        </FrameLayout>
        <!-- Surface View End -->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/pic_constrain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom">

            <com.perol.asdpl.pixivez.view.NiceImageView
                android:id="@+id/imageview_user"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="1dp"
                app:border_color='?attr/colorPrimary'
                app:border_width="2.6dp"
                app:is_circle="true"
                android:hapticFeedbackEnabled="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:maxLines="2"
                android:singleLine="false"
                android:text="圣诞特辑"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageview_user" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/save"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:minHeight="0dp"
                android:text="@string/save" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/like"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:minHeight="0dp"
                android:text="@string/like"
                android:textColor="?attr/colorPrimary" />
        </LinearLayout>
    </LinearLayout>


</com.google.android.material.card.MaterialCardView>

