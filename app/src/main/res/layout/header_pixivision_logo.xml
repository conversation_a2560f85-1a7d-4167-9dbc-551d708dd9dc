<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/header_logo"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="0dp"
    android:layout_gravity="center"
    android:padding="0dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="3dp">

    <ImageView
        android:background="#fffafafa"
        android:id="@+id/logo"
        android:layout_width="wrap_content"
        android:layout_height="210dp"
        android:layout_gravity="center_horizontal"
        android:minWidth="330dp"
        android:paddingHorizontal="20dp"
        android:src="@drawable/pixivision_color_logo"
        android:contentDescription="pixivision" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="15dp"
        android:text="@string/pixivision_desc2"
        android:textAlignment="center"
        android:textColor="@color/dark"
        android:textSize="16sp" />
</com.google.android.material.card.MaterialCardView>
