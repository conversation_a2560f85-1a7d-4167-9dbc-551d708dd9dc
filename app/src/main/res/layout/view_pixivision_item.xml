<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:paddingStart="8dp"
    android:paddingTop="8dp"
    android:paddingEnd="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <View
            android:id="@+id/pixivision_viewed"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:background="?attr/colorPrimary" />

        <ImageView
            android:id="@+id/imageView_pixivision"
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:src="@drawable/h" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">

            <TextView
                android:id="@+id/textView_pixivision_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="膝枕を描いたイラスト特集"
                android:textSize="18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
