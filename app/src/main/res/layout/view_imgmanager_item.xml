<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/item_img"
        android:layout_width="100dp"
        android:layout_height="100dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:maxLines="2"
        android:singleLine="false"
        android:textIsSelectable="true"
        android:textAlignment="textStart"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/item_img"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_target"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:singleLine="false"
        android:maxLines="2"
        tools:text="@{item.name}"
        android:textColor="?attr/colorAccent"
        android:textAlignment="textStart"
        android:textIsSelectable="true"
        app:layout_constraintTop_toBottomOf="@+id/item_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/item_img"/>

    <TextView
        android:id="@+id/item_pid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        tools:text="@{item.pid.toString()}"
        android:textColor="?attr/colorPrimary"
        android:textIsSelectable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/item_img"/>

    <TextView
        android:id="@+id/item_part"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        tools:text="@{item.part}"
        android:textColor="?attr/colorPrimary"
        android:textIsSelectable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/item_pid"/>

    <TextView
        android:id="@+id/item_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        tools:text="@{(item.size)}"
        android:textColor="?attr/colorPrimary"
        android:textIsSelectable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/item_part"/>

    <TextView
        android:id="@+id/item_pixel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text='@{(item.pixel)}'
        android:textColor="?attr/colorPrimary"
        android:textIsSelectable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/item_size"/>

    <TextView
            android:id="@+id/item_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            tools:text="@{(item.time)}"
            android:textColor="?attr/colorPrimary"
            app:layout_constraintStart_toEndOf="@+id/item_pixel"
            app:layout_constraintTop_toTopOf="@+id/item_pixel" />

    <CheckBox
        android:id="@+id/item_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>