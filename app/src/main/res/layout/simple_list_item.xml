<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

<!--    <com.google.android.material.card.MaterialCardView-->
<!--    android:layout_width="match_parent"-->
<!--    app:cardUseCompatPadding="true"-->
<!--    app:cardPreventCornerOverlap="true"-->
<!--    android:layout_height="wrap_content">-->
    <TextView
        tools:text="1"
        android:id="@+id/name"
        android:textSize="16sp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="0dp"
        android:layout_width="match_parent"
        android:gravity="center_vertical"
        android:layout_height="48dp" />
<!--    </com.google.android.material.card.MaterialCardView>-->
</LinearLayout>
