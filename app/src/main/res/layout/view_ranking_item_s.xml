<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardview"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginTop="4dp"
    android:layout_marginEnd="2dp"
    android:checkable="true"
    app:cardElevation="5dp">

    <ImageView
        android:id="@+id/item_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:maxHeight="720dp"
        android:src="?attr/colorSurface"
        android:transitionName="mainimage"
        android:contentDescription="main image" />

    <TextView
        android:id="@+id/textview_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="2dp"
        android:layout_gravity="end"
        android:alpha="0.3"
        android:background="#e0000000"
        tools:text="11"
        android:maxLines="1"
        android:textColor="@color/white"
        android:textSize="16sp"/>
    <com.perol.asdpl.pixivez.view.NiceImageView
        android:id="@+id/imageview_user"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_marginStart="-8dp"
        android:alpha="0.8"
        android:background="@drawable/round_ripple"
        tools:src="@mipmap/ic_noimage"
        android:translationX="-8dp"
        android:hapticFeedbackEnabled="true"
        app:border_color="@color/transparent"
        app:border_width="2dp"
        app:is_circle="true" />

    <com.perol.asdpl.pixivez.view.NiceImageView
        android:id="@+id/imageview_like"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="2dp"
        android:padding="2dp"
        android:alpha="0.9"
        android:src="@drawable/ic_love_outline"
        android:background="@drawable/round_ripple"
        android:hapticFeedbackEnabled="true"
        app:border_width="2dp"
        app:is_circle="true"
        android:layout_gravity="bottom|end"/>
</com.google.android.material.card.MaterialCardView>

