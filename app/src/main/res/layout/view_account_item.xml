<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/imageview_user"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textview_user"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toTopOf="@id/textview_email"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/imageview_user"
        app:layout_constraintTop_toTopOf="@+id/imageview_user"
        tools:text="Username" />

    <TextView
        android:id="@+id/textview_email"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="0dp"
        app:layout_constraintBottom_toBottomOf="@id/imageview_user"
        app:layout_constraintStart_toStartOf="@id/textview_user"
        app:layout_constraintTop_toBottomOf="@+id/textview_user"
        tools:text="Email" />

    <ImageButton
        android:id="@+id/imageview_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="?attr/colorControlNormal" />
</androidx.constraintlayout.widget.ConstraintLayout>
