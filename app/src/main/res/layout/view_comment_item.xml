<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_margin="5dp"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
        android:id="@+id/commentuserimage"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginBottom="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:src="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/comment_username"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
android:textColor="@color/colorAccent"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:textIsSelectable="true"
        tools:text="111111111"
        app:layout_constraintStart_toEndOf="@+id/commentuserimage"
        app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/reply_to_hit"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_marginStart="8dp"
            android:text="@string/reply_to"

            android:textAlignment="center"
            android:textColor="@color/colorPrimary"
            app:layout_constraintBottom_toTopOf="@+id/comment_detail"
            app:layout_constraintStart_toEndOf="@+id/comment_username" />

        <TextView
            android:id="@+id/comment_date"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_marginStart="8dp"
            tools:text="datetime"
            app:layout_constraintBottom_toTopOf="@+id/comment_detail"
            app:layout_constraintStart_toEndOf="@+id/reply_to_hit" />

    <TextView
        android:id="@+id/comment_detail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
android:textIsSelectable="true"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        tools:text="1111111122"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/commentuserimage"
        app:layout_constraintTop_toBottomOf="@+id/comment_username" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>