<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

<!--    <com.google.android.material.card.MaterialCardView-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:cardPreventCornerOverlap="true"-->
<!--        app:cardUseCompatPadding="true">-->

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:text="@string/summary" />
<!--    </com.google.android.material.card.MaterialCardView>-->

<!--    <com.google.android.material.card.MaterialCardView-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:cardPreventCornerOverlap="true"-->
<!--        app:cardUseCompatPadding="true">-->

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:layout_marginTop="24dp"
            android:text="@string/summary_thanks"
            android:textColor="?attr/colorPrimary"
            android:textSize="20sp" />

<!--    </com.google.android.material.card.MaterialCardView>-->
</LinearLayout>
