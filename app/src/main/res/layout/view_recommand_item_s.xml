<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardview"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginTop="4dp"
    android:layout_marginEnd="2dp"
    app:cardElevation="5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/item_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:maxHeight="720dp"
            android:src="@color/white"
            android:transitionName="mainimage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textview_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="2dp"
            android:layout_gravity="end"
            android:alpha="0.3"
            android:background="#e0000000"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="11"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="20sp"/>

        <com.perol.asdpl.pixivez.view.NiceImageView
            android:id="@+id/imageview_like"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="2dp"
            android:padding="2dp"
            android:alpha="0.9"
            android:src="@drawable/ic_love_outline"
            android:background="@drawable/round_ripple"
            android:hapticFeedbackEnabled="true"
            app:border_width="2dp"
            app:is_circle="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!--TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="36dp"
            android:maxLines="1"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/item_img" /-->

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>

