<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fragment"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.home.trend.HelloTrendingFragment">

    <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorPrimary"
            android:orientation="horizontal">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tablayout"
                style="@style/Widget.MaterialComponents.TabLayout.Colored"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                app:tabIndicatorFullWidth="false"
                app:tabMode="scrollable" />

            <TextView
                android:id="@+id/imageview_rank"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:padding="8dp"
                android:layout_gravity="center_vertical"
                android:elevation="5dp"
                android:text="@string/sort_by"
                android:textColor="?android:attr/textColorPrimaryInverse"
                app:drawableLeftCompat="@drawable/ic_action_rank"
                app:drawableTint="?android:attr/textColorPrimaryInverse"
                android:drawablePadding="5dp"
                android:background="?attr/selectableItemBackground" />
        </LinearLayout>
    </com.google.android.material.appbar.AppBarLayout>
    <androidx.viewpager2.widget.ViewPager2
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    <!--androidx.fragment.app.FragmentContainerView
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:id="@+id/content_view"
        android:layout_height="match_parent"
        android:layout_width="match_parent" /-->

</LinearLayout>