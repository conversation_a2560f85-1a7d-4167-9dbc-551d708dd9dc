<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_host"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:hint="tag">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:imeOptions="actionDone"
                android:singleLine="true" />
        </com.google.android.material.textfield.TextInputLayout>

        <ImageButton
            android:id="@+id/add"
            android:layout_width="wrap_content"
            android:background="@android:color/transparent"
            android:layout_gravity="end|center"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_add_tag"
            android:layout_margin="10dp"
            android:padding="8dp"
            android:contentDescription="@string/add" />
    </FrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>