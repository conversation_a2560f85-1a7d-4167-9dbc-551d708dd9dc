<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.home.pixivision.PixivsionActivity">

    <com.google.android.material.appbar.AppBarLayout
            android:fitsSystemWindows="true"
            android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="5dp"
        android:background="@color/white">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"

            android:layout_height="wrap_content"
            app:contentScrim="?attr/colorPrimary"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <TextView
                android:layout_marginStart="25dp"
                android:layout_marginTop="18dp"
                android:layout_marginBottom="18dp"
                android:layout_marginEnd="25dp"
                android:textColor="@color/dark"
                android:text="@string/pixivision_desc2"
                android:layout_gravity="bottom|center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                app:drawableTopCompat="@drawable/pixivision_color_logo" />

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toobar_pixivision"
                android:layout_height="?attr/actionBarSize"
                android:layout_width="match_parent"
                app:title=" "
                app:layout_collapseMode="pin" />


        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_pixivision"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"

        />

</androidx.coordinatorlayout.widget.CoordinatorLayout>