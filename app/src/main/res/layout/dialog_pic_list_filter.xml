<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.Group
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:constraint_referenced_ids="filter_title,toggleButton,divider1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/filter_title"
            android:paddingHorizontal="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/state_filter"
            android:layout_gravity="center_vertical"
            style="@style/TextAppearance.AppCompat.Title" />

        <Space
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <CheckBox
            android:id="@+id/show_blocked"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/show_block" />

        <Space
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />
    </LinearLayout>
    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_bookmark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="10dp"
        android:layout_gravity="center_horizontal"
        app:singleSelection="false"
        app:selectionRequired="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_bookmarked"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/bookmarked" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_not_bookmarked"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/not_bookmarked" />
    </com.google.android.material.button.MaterialButtonToggleGroup>
    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_download"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="10dp"
        android:layout_gravity="center_horizontal"
        app:singleSelection="false"
        app:selectionRequired="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_downloaded"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/downloaded" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_not_downloaded"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/not_downloaded" />
    </com.google.android.material.button.MaterialButtonToggleGroup>
    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_follow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="10dp"
        android:layout_gravity="center_horizontal"
        app:singleSelection="false"
        app:selectionRequired="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_followed"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/followings" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_not_followed"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/not_followed" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_AI"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="10dp"
        android:layout_gravity="center_horizontal"
        app:singleSelection="false"
        app:selectionRequired="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_AI_none"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/artificial" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_AI_half"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/assisted" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_AI_full"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/ai_generated" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="10dp"
        android:layout_gravity="center_horizontal"
        app:singleSelection="false"
        app:selectionRequired="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_illust"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/illust" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_manga"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/manga" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_Ugoira"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/ugoira" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_restrict"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingHorizontal="10dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:selectionRequired="true"
        app:singleSelection="false"
        tools:visibility="visible">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_public"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/publics" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_private"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="xRestrict" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ems="10"
        android:text="@string/max_sanity"
        android:layout_marginStart="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.slider.Slider
        android:id="@+id/slider_sanity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:valueFrom="1"
        android:valueTo="7"
        android:value="7"
        android:stepSize="1" />

    <View
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider"
        app:layout_constraintTop_toBottomOf="@+id/toggleButton" />

    <TextView
        android:layout_margin="8dp"
        android:id="@+id/span_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/layout_config"
        android:layout_gravity="center_vertical"
        style="@style/TextAppearance.AppCompat.Title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_show_user_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="10dp"
        android:layout_gravity="center_horizontal"
        app:singleSelection="true"
        app:selectionRequired="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_user_img"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/show_user_img" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/hide_user_img"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/hide_user_img" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggle_button"
        android:paddingHorizontal="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        app:singleSelection="true"
        app:selectionRequired="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/show_save_btn"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/show_button_layout" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/hide_save_btn"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/no_button_layout" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

    <TextView
        android:layout_marginStart="8dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/span_num" />
    <com.google.android.material.slider.Slider
        android:id="@+id/slider_span"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:valueFrom="0"
        android:valueTo="6"
        android:value="2"
        android:stepSize="1"/>
    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider" />
</LinearLayout>