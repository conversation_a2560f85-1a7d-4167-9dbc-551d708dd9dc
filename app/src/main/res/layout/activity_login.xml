<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:fitsSystemWindows="true"
    tools:context=".ui.account.LoginActivity">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/transparent"
        android:fitsSystemWindows="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="PxEz"
        android:textColor="?attr/colorPrimaryDark"
        android:textSize="80sp"
        app:layout_constraintBottom_toTopOf="@id/accountTextInputLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.382"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/accountTextInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:visibility="invisible"
        app:endIconMode="clear_text"
        app:helperText="@string/account_helper"
        app:layout_constraintBottom_toTopOf="@id/passwordTextInputLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textView1">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_username"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/account"
            android:imeOptions="actionNext"
            android:inputType="text"
            android:maxLines="1"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/passwordTextInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/loginBtn"
        app:layout_constraintEnd_toEndOf="@id/accountTextInputLayout"
        app:layout_constraintStart_toStartOf="@id/accountTextInputLayout"
        app:layout_constraintTop_toBottomOf="@id/accountTextInputLayout"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/password"
            android:imeOptions="actionDone"
            android:inputType="textPassword"
            android:maxLines="1"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/loginBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="?attr/colorPrimary"
        android:text="@string/login"
        android:textSize="18sp"
        app:icon="@drawable/ic_done_black"
        app:iconGravity="textStart"
        app:layout_constraintBottom_toTopOf="@id/textview_help"
        app:layout_constraintEnd_toEndOf="@id/passwordTextInputLayout"
        app:layout_constraintStart_toStartOf="@id/passwordTextInputLayout"
        app:layout_constraintTop_toBottomOf="@id/passwordTextInputLayout" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/textview_help"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/login_help"
        android:textColor="@color/md_red_500"
        android:textSize="18sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/loginBtn"
        app:layout_constraintStart_toStartOf="@id/loginBtn"
        app:layout_constraintTop_toBottomOf="@id/loginBtn"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/token_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/token_login"
        android:textColor="?attr/colorPrimary"
        app:layout_constraintStart_toStartOf="@id/loginBtn"
        app:layout_constraintTop_toBottomOf="@id/textview_help" />
    <TextView
        android:id="@+id/register"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/donthaveaccount"
        android:textColor="?attr/colorPrimary"
        app:layout_constraintEnd_toEndOf="@id/loginBtn"
        app:layout_constraintTop_toBottomOf="@id/textview_help" />

</androidx.constraintlayout.widget.ConstraintLayout>
