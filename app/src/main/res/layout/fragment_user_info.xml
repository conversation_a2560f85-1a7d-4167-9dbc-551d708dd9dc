<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.user.UserInfoFragment">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:attr/colorBackground">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="14dp"
            app:cardCornerRadius="5dp"
            app:cardElevation="3dp"
            app:contentPadding="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:minHeight="50dp">

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/user_info"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/textview_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="ID"
                    android:textIsSelectable="true"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textView1"
                    app:layout_constraintStart_toEndOf="@+id/textView1"
                    tools:ignore="HardcodedText" />

                <com.google.android.material.chip.Chip
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/textview_follower"
                    android:text="@string/followers"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/chipgroup"
                    app:layout_constraintTop_toBottomOf="@+id/textView1" />

                <com.google.android.material.chip.Chip
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/textview_following"
                    android:layout_marginStart="8dp"
                    android:text="@string/followings"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textview_follower"
                    app:layout_constraintStart_toEndOf="@+id/textview_follower" />

                <TextView
                    android:id="@+id/textview_following_num"
                    android:layout_width="wrap_content"
                    android:layout_height="25dp"
                    android:layout_marginBottom="0dp"
                    android:layout_marginStart="8dp"
                    tools:text="0"
                    android:textColor="?attr/colorPrimary"
                    android:textSize="20sp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textview_following"
                    app:layout_constraintStart_toEndOf="@+id/textview_following" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/textview_fans"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="@string/goodpfriend"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textview_following"
                    app:layout_constraintStart_toEndOf="@+id/textview_following_num" />

                <TextView
                    android:id="@+id/textview_fans_num"
                    android:layout_width="wrap_content"
                    android:layout_height="25dp"

                    android:layout_marginBottom="0dp"
                    android:layout_marginStart="8dp"
                    tools:text="0"
                    android:textColor="?attr/colorPrimary"
                    android:textSize="20sp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textview_following"
                    app:layout_constraintStart_toEndOf="@+id/textview_fans" />

                <com.google.android.material.chip.ChipGroup
                    android:minHeight="100dp"
                    app:chipSpacing="2dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:id="@+id/chipgroup"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:layout_height="20dp"
                    app:layout_constraintTop_toBottomOf="@id/textview_fans" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>


        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="14dp"
            android:minHeight="150dp"
            android:id="@+id/cardView_usercomment"

            app:cardCornerRadius="5dp"
            app:cardElevation="3dp"
            app:contentPadding="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:layout_marginEnd="14dp"
                android:text="@string/ta" />
            <TextView
                android:id="@+id/textView_usercomment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textIsSelectable="true"
                android:paddingTop="15dp"
                android:text="~" />

        </com.google.android.material.card.MaterialCardView>

        <ImageView
            android:id="@+id/imageview_user_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="600dp"
            android:paddingBottom="80dp"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            android:layout_margin="10dp"
            tools:src="@drawable/chobi01"
            android:contentDescription="user profile background" />
    </LinearLayout>
</androidx.core.widget.NestedScrollView>