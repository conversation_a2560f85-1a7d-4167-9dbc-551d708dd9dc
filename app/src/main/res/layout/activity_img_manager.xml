<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.settings.HistoryFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                android:theme="@style/MyToolbar"
                app:title="@string/img_manager"
                app:titleTextColor="?android:attr/textColorPrimaryInverse" />
        </com.google.android.material.appbar.AppBarLayout>

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/swith_filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:hint="@string/length_filter_desc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout" />

        <TextView
            android:id="@+id/img_count"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:paddingEnd="40dp"
            app:layout_constraintEnd_toEndOf="@id/swith_once"
            app:layout_constraintStart_toStartOf="@id/swith_filter"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout" />

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/swith_once"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:hint="@string/all_rename"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview_img_manager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipToPadding="false"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/swith_filter" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_start"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="140dp"
            android:layout_marginBottom="18dp"
            android:backgroundTint="@color/md_red_600"
            android:src="@drawable/ic_action_search"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_settings"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="80dp"
            android:layout_marginBottom="18dp"
            android:backgroundTint="@color/yellow"
            android:src="@drawable/ic_settings_outline"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_folder"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="18dp"
            android:src="@drawable/ic_action_folder"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
