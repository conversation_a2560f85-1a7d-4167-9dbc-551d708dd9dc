<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:clipToPadding="true"
    android:paddingStart="8dp"
    android:paddingTop="8dp"
    android:paddingEnd="8dp"
    app:cardElevation="3dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imageView_pixivision"
            android:layout_width="320dp"
            android:layout_height="180dp"
            android:adjustViewBounds="true"
            android:layout_gravity="center_horizontal"
            android:minWidth="250dp"
            android:scaleType="fitCenter"
            android:src="@drawable/pixivision_color_logo" />

        <TextView
            android:id="@+id/textView_pixivision_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="2dp"
            android:layout_marginBottom="1dp"
            android:maxWidth="220dp"
            android:singleLine="true"
            tools:text="膝枕を描いたイラスト特集"
            android:textColor="?android:textColorPrimary"
            android:textSize="14sp" />

        <View
            android:id="@+id/pixivision_viewed"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:background="?attr/colorPrimary" />

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
