<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dark"
    tools:ignore="Overdraw">

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpage_zoom"
        android:layout_width="match_parent"
        android:layout_height="match_parent"

        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textview_zoom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center"
        android:layout_marginBottom="10dp"
        android:singleLine="true"
        android:text="1/1"
        android:textColor="@color/white"
        android:textSize="15sp" />
</FrameLayout>