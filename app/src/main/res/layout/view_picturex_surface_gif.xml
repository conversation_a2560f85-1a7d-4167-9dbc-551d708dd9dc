<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="300dp"
    android:orientation="vertical">

    <com.perol.asdpl.pixivez.view.AnimationView
        android:id="@+id/imageview_gif"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center" />

    <ImageView
        android:id="@+id/preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:transitionName="mainimage" />


    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progressbar_gif"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        app:trackThickness="4dp"
        app:trackColor="@color/white"
        app:indicatorSize="50dp"
        app:trackCornerRadius = "2dp"/>

    <ImageView
        android:id="@+id/imageview_play"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center"
        android:layout_marginStart="2dp"
        android:alpha="0.9"
        android:src="@drawable/ic_action_play"
        app:tint="@color/white" />

</FrameLayout>