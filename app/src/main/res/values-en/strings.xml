<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_features">"Some features hidden in the menu or long press, and more by intuition attempt\n"</string>
    <string name="app_help">
        "0. This is a free app. If you paid for it, please immediately ask for a refund and make a negative comment! \n"
        "Make sure to install and update from the store or the GitHub releases, third party packages might be problematic or out-of-date.\n"
        "1. Long press on the picture details page to save the selected picture, and long press the head portrait to quickly follow the author, please provide storage access for saving.\n"
        "2. Most buttons have intuitive long press special operations (such as setting tags when like any pictures), or hiding them in the menu, you can try more\n"
        "3. When browsing a moving picture, click the middle 0% progress bar to start downloading. After playing, press and hold to save the composition. The memory cost of the conversion process is quite large, and occasional crashes are to be expected.\n"
        "4. If the animation cannot be played, please exit the page or clear the cache and try again, which will usually work.\n"
        "5. This is a personal project, for feedback please send an email to the address displayed on the settings page or submit an issue on GitHub. Your energy and ability are limited. Please do not use extreme methods for feedback. Be considerate of developers, and you're welcome to join development and design.\n"
        "6. If you encounter the problem of flashback after the update, please try to clear the application data and update to the latest version. Also send a feedback with the error message or log to the developer, this is usually effective.\n"
        "7. The main switch of R limit is in the official website, if you find illustration without permission, you can open the switch by yourself. The developer can not provide help to this\n"
        "8. The in-app account registration support has been suspended, please register on the official website.\n"
        "9. This application does not collect unnecessary information except for crash information\n"
        "\nYou can reopen this help on the settings page"
    </string>
    <string name="login_help_md"># Troubleshooting\n\nIf you are receive this when trying to log in:\n\n```shell\n    http 400 bad request\n```\n\nPlease try the following steps:\n\n1. Make sure the application is up to date, go to one of the official sites linked on GitHub to get the latest version\n2. Check if the access point APN is Net instead of Wap\n3. If it is the latest version, the username or password is wrong (104:), you need to check the account password\n4. The account is not verified or the user account is invalid (103:), you need to go to the official website for verification\n\nps: account password refers to pixiv account password, not github\n\nIf prompted after login\n\n```shell\n    Program error, about to exit, xx resourece not found\n```\n\n1. Make sure the application is up to date, go to one of the official sites linked on GitHub to get the latest version\n2. Make sure that the application is obtained from one of the official sites, not from others\n3. After installing the latest version from the above channels, clear the application data and try to log in again\n\n## If nothing helps\n\nPlease use the feedback email or GitHub Issues, provide details like version number, system information, error message screenshots\n\nBefore giving feedback, you need to make sure\n\n1. The application is obtained from one of the official sites linked on GitHub\n2. Did not use the early adopter system or use the xposed or magisk module to make magic changes\n3. You have read carefully \"please read it all\" in settings</string>
    <string name="view_cache_repo">In-mem Caches</string>
    <string name="follow">follow</string>
    <string name="following">Following</string>
    <string name="following_private">(Following)</string>
    <string name="click">click</string>
    <string name="support_string">Feedback: via GitHub or by email: <EMAIL></string>
    <string name="current_developer">Current developer</string>
    <string name="support_text">Your support will motivate me to equip this app with more useful features~</string>
    <string name="support_popup_title">Support via..</string>
    <string name="support_static">The No. of pics you have collected is: %1$s !</string>
    <string name="ali">AliPay</string>
    <string name="wechat">WeChat</string>
    <string name="thanks_to">Thanks</string>
    <string name="thanks">Thanks for your help/proposal: 腐奇，爱酱,Snake，PoisonBCat,涛灬,万物皆虚,繁落,碘酒,betray,right now,niconiconi,firok,YAMAHA,OneMice,SC61，果干辣，吸血姬，逢田梨香子的老公,吃猫的咸鱼,etc</string>
    <string name="img_manager">Pictures</string>
    <string name="checkupdate">Check for updates</string>
    <string name="_18">Show R-18 image previews</string>
    <string name="pic_quality">Picture quality</string>
    <string name="pic_quality_download">Download picture quality</string>
    <string name="origin">Show original</string>
    <string name="searchsource">SauceNAO</string>
    <string name="clear_cache">Clear cache</string>
    <string name="view_history">History</string>
    <string name="appsetting">Settings</string>
    <string name="logout">Log out</string>
    <string name="account">Account</string>
    <string name="account_helper">Pixiv ID or email</string>
    <string name="password">Password</string>
    <string name="login">Login</string>
    <string name="login_help">Login help</string>
    <string name="donthaveaccount">No account?</string>
    <string name="error_blank_account">Account cannot be blank</string>
    <string name="error_blank_password">Password cannot be blank</string>
    <string name="error_invalid_account_password">Incorrect account or password</string>
    <string name="saveformat">Save format</string>
    <string name="filesaveformat">Filename format</string>
    <string name="filesaveformatstring">Enter format string:</string>
    <string name="Illust_id_desc">illust ID</string>
    <string name="Title_desc">title</string>
    <string name="Painter_id_desc">artist ID</string>
    <string name="Painter_Name_desc">artist name</string>
    <string name="Painter_Account_desc">artist account</string>
    <string name="Which_part_desc">picture # (omitted if single)</string>
    <string name="type_desc">extension (like .jpg / .png)</string>
    <string name="tag_desc">tags (automatically deduplicated)</string>
    <string name="tag_separator">tag separator</string>
    <string name="savepath">Save path</string>
    <string name="startpage">Initial page</string>
    <string name="try_to_login">trying</string>
    <string name="pixel">pixel</string>
    <string name="view">views</string>
    <string name="bookmark">marks</string>
    <string name="view_comment">show comment</string>
    <string name="related">related</string>
    <string name="illustid">illust id</string>
    <string name="searchhistory">search history (long press to delete)</string>
    <string name="clearhistory">clear history</string>
    <string name="hottags">hot tags (try long press~)</string>
    <string name="newwork">new</string>
    <string name="oldwork">old</string>
    <string name="part">part</string>
    <string name="complete">complete</string>
    <string name="title">title</string>
    <string name="by">by</string>
    <string name="search_by">search by</string>
    <string name="search">search for</string>
    <string name="same_as_tag">some as tag</string>
    <string name="painterid">Artist ID</string>
    <string name="word">WORD</string>
    <string name="goodpfriend">P</string>
    <string name="ta">about</string>
    <string name="user_info">information</string>
    <string name="theme">Theme</string>
    <string name="setting">Settings</string>
    <string name="savesuccess">saved!</string>
    <string name="alreadysaved">already saved</string>
    <string name="registerclose">In-app registration has been disabled, please register at pixiv.net</string>
    <string name="api_config">API Config</string>
    <string name="encode_gif">Encode as gif</string>
    <string name="save_zip">Save as zip</string>
    <string name="savefirst">Save preview</string>
    <string name="fetchtags">fetch tags....</string>
    <string name="publics">Public</string>
    <string name="privates">Private</string>
    <string name="reply_to">Reply to</string>
    <string name="illust">Illust</string>
    <string name="manga">Manga</string>
    <string name="abouts">About</string>
    <string name="painter">Artist</string>
    <string name="new1">New</string>
    <string name="saveselectpic1">Save?</string>
    <string name="confirm">confirm</string>
    <string name="multichoicesave">Multiple selection</string>
    <string name="choice">Choice</string>
    <string name="save">Save</string>
    <string name="download">Download</string>
    <string name="prompt_email">Email</string>
    <string name="prompt_password">Password</string>
    <string name="action_sign_in">Sign in or register</string>
    <string name="action_sign_in_short">Sign in</string>
    <string name="add">Add account</string>
    <string name="logoutallaccount">Log out of all accounts?</string>
    <string name="savegifsuccess">Saved ugoira</string>
    <string name="savegifsuccesserr">Ugoira save error</string>
    <string name="crash_report">Crash reports</string>
    <string name="feed_back">Feedback</string>
    <string name="needstatusbar">Status bar</string>
    <string name="refreshTab">Automatic tab refresh</string>
    <string name="refreshTab_summary">Enable to reduce memory usage</string>
    <string name="changeicons">Change icons</string>
    <string name="supporttitle">Support</string>
    <string name="view_report">View report</string>
    <string name="needtorestart">Restart needed to apply!</string>
    <string name="collectmode">Collection mode</string>
    <string name="animation">Animation</string>
    <string name="animation_summary">Disable to reduce memory usage</string>
    <string name="show_user_img_searchr">Show artist avatar in search results</string>
    <string name="show_user_img_searchr_summary">Large style card, can save some memory on close</string>
    <string name="show_user_img_bookmarked">Show artist avatar in bookmark view</string>
    <string name="show_user_img_bookmarked_summary">Large style card, can save some memory on close</string>
    <string name="show_user_img_main">Show artist avatar in home</string>
    <string name="show_user_img_main_summary">Large style card, can save some memory on close</string>
    <string name="new_banner">New banner format (slider)</string>
    <string name="banner_summary">Pixivision spotlight banner</string>
    <string name="banner_auto_loop">Loop banner</string>
    <string name="download_progress">Downloads</string>
    <string name="join_download_queue">Added to download queue</string>
    <string name="again_to_exit">Press again to exit</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="restart_now">Restart Now</string>
    <string name="restart">Restart</string>
    <string name="account_management">Accounts</string>
    <string name="auto_check_update">Auto check update</string>
    <string name="bookmark_private">private</string>
    <string name="bookmark_public">public</string>
    <string name="start_date">Start Date</string>
    <string name="end_date">End Date</string>
    <string name="choose_date">Date</string>
    <string name="hide_bookmarked">Hide bookmarked pic</string>
    <string name="summary">When I first came into contact with Java, I learned from the official docs while developing, and groped to complete this client. Because of my skills there were many embarrassing bugs and optimization problems. In the process of improving this application, I got a lot developer experience and relatively profound lessons have also been helped and supported by many people.</string>
    <string name="title_change_theme">Change accent</string>
    <string name="title_save_path">Save path</string>
    <string name="title_change_icon">Change icons</string>
    <string name="title_theme_color">Accent color</string>
    <string name="action_apply">Apply</string>
    <string name="action_select">Select</string>
    <string name="action_change">Change</string>
    <string name="create_painter_folder">Separate folder for each artist</string>
    <string name="create_separate_folder">Per-artist folders</string>
    <string name="create_R18_folder">Separate R-18 folder</string>
    <string name="R18_folder">Custom R-18 folder\n (default: xRestrict/)</string>
    <string name="R18_private">Mark as private when bookmarking R-18 pictures</string>
    <string name="refresh_token">Refreshing token..</string>
    <string name="refresh_token_fail">Refresh token failed</string>
    <string name="translate">Translate</string>
    <string name="back_to_the_top">Double-click back to the top</string>
    <string name="add_to_block_tag_list">Add to block tag list</string>
    <string name="block_tag">Blocked tags</string>
    <string name="title_activity_manager_settings">Settings</string>
    <string name="max_task_num">Max task count</string>
    <string name="thread_num">Thread count</string>
    <string name="task_setting">Settings</string>
    <string name="all_resume">Resume all</string>
    <string name="all_stop">Stop all</string>
    <string name="all_cancel">Clear queue</string>
    <string name="finished_cancel">Remove finished</string>
    <string name="unfinished_task">Resume unfinished tasks</string>
    <string name="show_download_toast">Show download toast</string>
    <string name="resume_unfinished_task_on">Resume on startup</string>
    <string name="resume_unfinished_task_off">Keep, but do not resume</string>
    <string name="already_encoding">Already encoding</string>
    <string name="save_zip_success">Zip saved</string>
    <string name="permission_denied">Permission denied, please enable it in system settings</string>
    <string name="cache_clear_message">This will clear the cache.</string>
    <string name="ok">OK</string>
    <string name="confirm_title">Delete?</string>
    <string name="wrong_id">wrong id</string>
    <string name="error_unknown">Unknown error, please check your connection</string>
    <string name="login_success">Login successful</string>
    <string name="share">Share</string>
    <string name="saucenao_compress_success" tools:ignore="TypographyEllipsis">Uploading...</string>
    <string name="saucenao_upload_success" tools:ignore="TypographyEllipsis">Uploaded, processing...</string>
    <string name="saucenao_upload_error">"Upload failed: "</string>
    <string name="uploading">Uploading</string>
    <string name="upload_success">Upload finished, please reload this page</string>
    <string name="copied">Copied</string>
    <string name="saved">Saved</string>
    <string name="username">Username</string>
    <string name="account_create_button">Create account</string>
    <string name="account_create_recommed">please register at pixiv.net</string>
    <string name="pixivision_desc">PIXIV: a creative media that delivers the \"the best works of creative originality including drawings, manga, and novels\"</string>
    <string name="use_ssl">Use SSL</string>
    <string name="search_results">Search results</string>
    <string name="attention">Attention</string>
    <string name="customformat_token">Token</string>
    <string name="customformat_result">Result (click to insert)</string>
    <string name="summary_thanks">Thanks to:</string>
    <string name="followings">Following</string>
    <string name="followers">Followers</string>
    <string name="pixivision_desc2">a creative media that delivers \n \"the best works of creative originality\"</string>
    <string name="change_to_first">change to first</string>
    <string name="notsf_subtext">Development, testing, release</string>
    <string name="loading" tools:ignore="TypographyEllipsis">Loading...</string>
    <string name="needstatusbar_subtext">For notched screens</string>
    <string name="ultranity_subtext">Current maintainer</string>
    <string name="notsf_subtext2">Original author</string>
    <string name="rightnow_subtext">Interface design and icons</string>
    <string name="autocheck_subtext">No effect on Play version</string>
    <string name="clipboard_detected">Pid in clipboard detected</string>
    <string name="jumpto">open: </string>
    <string name="h_is_ok">H is OK! ԅ(¯﹃¯ԅ)</string>
    <string name="h_not_ok">H not OK! (￣^￣)</string>
    <string name="length_filter_desc">Only 50-</string>
    <string name="all_rename">rename without check</string>
    <string name="not_this_one">not this one</string>
    <string name="I_know">OK, I know</string>
    <string name="read_it">please read it</string>
    <string name="hide_downloaded">Hidden Downloaded</string>
    <string name="only_bookmarked">Bookmarked Only</string>
    <string name="comment_successful">Comment successful!</string>
    <string name="rate_limited">Rate limited.</string>
    <string name="enableonlybookmarked">Showing only allow collection</string>
    <string name="supportsummary">If you think this app is useful, support us!</string>
    <string name="welcomePR">If you want and have the ability to help improve this application, submit PR to us!</string>
    <string name="donate">Donate</string>
    <string name="sample_title">Sample: click auto set</string>
    <string name="hide_downloaded_detail">Retrieve the current default download path and sub-folders, supporting the use of python scripts to get more Gallery pid record on PC, provided as a convenience <a href ="https://wwa.lanzoux.com/iQB4Rigc7yh"> PC packaged program here</a> </string>
    <string name="hide_downloaded_summary">Hide download function can record additional non-native:</string>
    <string name="all">All</string>
    <string name="sortPic">Sort / filter</string>
    <string name="sort_by">Sort by</string>
    <string name="create_time">Created time</string>
    <string name="default_config">Default</string>
    <string name="other">Other</string>
    <string name="PR">submit Pull Request</string>
    <string name="action_export">Export</string>
    <string name="action_import">Import</string>
    <string name="mirror">Mirror</string>
    <string name="format">format</string>
    <string name="mirror_link_hint">i.pximg.net mirror website set in the URL, such as i.pixiv.cat, original.img.pixivic.net, etc.</string>
    <string name="params_desc">Original parameters, same to {time}/{illustid}_p{part}{type}</string>
    <string name="api_mirror">API Mirror</string>
    <string name="icon_draw_by">Drawing by:你主人(探姬);HK流星(MD) </string>
    <string name="set_mirror">Click to set the mirror website</string>
    <string name="login_expired">Login expired, please login again</string>
    <string name="token_expired">Token expired, please wait for refreshing</string>
    <string name="login_help_new">Due to Pixiv verification restriction, login from China\'s mainland is required to prepare VPN in advance by yourself</string>
    <string name="open_in_external_browser">Click here to open in browser (may fix error)</string>
    <string name="needactionbar">Show ActionBar</string>
    <string name="needactionbar_subtext">Back and share buttons</string>
    <string name="use_bottom_appbar">Use bottom AppBar layout</string>
    <string name="use_bottom_appbar_subtext">up side down~</string>
    <string name="use_picX_layout_main">Use new Card UI in main page</string>
    <string name="use_picX_layout_subtext">modern view UX</string>
    <string name="dynamicColorAPIAlert">Material3 DynamicColors needs Android 12/S+ (API 31+)</string>
    <string name="hold_to_delete">long press to delete</string>
    <string name="bookmarked">collected</string>
    <string name="not_bookmarked">Not favorited</string>
    <string name="downloaded">Downloaded</string>
    <string name="not_downloaded">not downloaded</string>
    <string name="state_filter">Status filter</string>
    <string name="not_followed">Not followed</string>
    <string name="show_user_img">show avatar</string>
    <string name="no_button_layout">Simplified layout</string>
    <string name="show_button_layout">key layout</string>
    <string name="hide_user_img">hide avatar</string>
    <string name="layout_config">Layout settings</string>
    <string name="span_num">Number of columns (default portrait 2 landscape 4)</string>
    <string name="background">background</string>
    <string name="avatar">user avatar</string>
    <string name="crash_title">Send issue to developer if app crash~</string>
    <string name="update_now">Download update now?</string>
    <string name="update_available">New update available</string>
    <string name="no_update">No update found</string>
    <string name="update_failed">Access update failed</string>
    <string name="token_login">Token Login</string>
    <string name="token_warning">⚠Warning！It is VERY DANGEROUS to access OAuth Token, DO NOT SHARE with others</string>
    <string name="dynamic_color">Dynamic Color</string>
    <string name="ai_type">AI type</string>
    <string name="artificial">No AI</string>
    <string name="assisted">Half AI</string>
    <string name="ai_generated">Full AI</string>
    <string name="select_reverse">Reverse Selection</string>
    <string name="flip">Flip</string>
    <string name="dislike">Undo Bookmark</string>
    <string name="walk_through">Random</string>
    <string name="show_block">show filtered view</string>
    <string name="recommend">Recommend</string>
    <string name="harmonize_color">Harmonize Color</string>
    <string name="harmonizeColorAPIAlert">Only for Android 11/R+ (API 30+)</string>
    <string name="changeing_icon_tip">icon changed, waiting for launcher update</string>
    <string name="max_sanity">max sanity</string>
    <string name="check_clipboard">check if clipboard contains pid</string>
    <string name="go_to_detail">go to detail</string>
    <string name="ip_download">Force DNS Solving</string>
    <string name="block_user">Block User</string>
    <string name="i_pximg_net_ip_list">customize IP list for i.pximg.net, seperate with \",\"</string>
    <string name="ip_shuffle">Load Balancing</string>
    <string name="restrict_sanity">sanity threshold for x restrict</string>
    <string name="auto_load_related_illust">Autoload related Illust</string>
    <string name="refreshDNS">Refresh DNS</string>
</resources>