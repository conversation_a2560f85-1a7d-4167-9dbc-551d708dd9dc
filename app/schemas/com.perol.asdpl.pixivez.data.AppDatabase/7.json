{"formatVersion": 1, "database": {"version": 7, "identityHash": "ceb49fc06e3dd973954d447ba8667463", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userid` INTEGER NOT NULL, `username` TEXT NOT NULL, `useremail` TEXT NOT NULL, `ispro` INTEGER NOT NULL, `userimage` TEXT NOT NULL, `Device_token` TEXT NOT NULL, `Refresh_token` TEXT NOT NULL, `Authorization` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "userid", "columnName": "userid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "useremail", "columnName": "useremail", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ispro", "columnName": "ispro", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userimage", "columnName": "userimage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Device_token", "columnName": "Device_token", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Refresh_token", "columnName": "Refresh_token", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Authorization", "columnName": "Authorization", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}, {"tableName": "blockTag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `translateName` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "translateName", "columnName": "translateName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'ceb49fc06e3dd973954d447ba8667463')"]}}