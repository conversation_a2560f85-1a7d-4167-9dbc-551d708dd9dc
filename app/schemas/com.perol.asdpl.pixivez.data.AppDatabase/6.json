{"formatVersion": 1, "database": {"version": 6, "identityHash": "98d0d648eee6ccd6088c345e8c22060b", "entities": [{"tableName": "history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`word` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "word", "columnName": "word", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}, {"tableName": "illusthistory", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`illustid` INTEGER NOT NULL, `imageurl` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT)", "fields": [{"fieldPath": "illustid", "columnName": "illustid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}, {"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userid` INTEGER NOT NULL, `username` TEXT NOT NULL, `useremail` TEXT NOT NULL, `ispro` INTEGER NOT NULL, `userimage` TEXT NOT NULL, `Device_token` TEXT NOT NULL, `Refresh_token` TEXT NOT NULL, `Authorization` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "userid", "columnName": "userid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "useremail", "columnName": "useremail", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ispro", "columnName": "ispro", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userimage", "columnName": "userimage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Device_token", "columnName": "Device_token", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Refresh_token", "columnName": "Refresh_token", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Authorization", "columnName": "Authorization", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}, {"tableName": "illusts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`url` TEXT NOT NULL, `illustid` INTEGER NOT NULL, `userid` INTEGER NOT NULL, `part` TEXT, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "illustid", "columnName": "illustid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userid", "columnName": "userid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "part", "columnName": "part", "affinity": "TEXT", "notNull": false}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}, {"tableName": "blockTag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `translateName` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "translateName", "columnName": "translateName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '98d0d648eee6ccd6088c345e8c22060b')"]}}