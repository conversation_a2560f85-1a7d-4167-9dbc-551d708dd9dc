{"formatVersion": 1, "database": {"version": 8, "identityHash": "4df900053eb7dd285d57c197c04f64e7", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userid` INTEGER NOT NULL, `username` TEXT NOT NULL, `useremail` TEXT NOT NULL, `ispro` INTEGER NOT NULL, `x_restrict` INTEGER NOT NULL DEFAULT 0, `userimage` TEXT NOT NULL, `Device_token` TEXT NOT NULL, `Refresh_token` TEXT NOT NULL, `Authorization` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "userid", "columnName": "userid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "useremail", "columnName": "useremail", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ispro", "columnName": "ispro", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "x_restrict", "columnName": "x_restrict", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "userimage", "columnName": "userimage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Device_token", "columnName": "Device_token", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Refresh_token", "columnName": "Refresh_token", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Authorization", "columnName": "Authorization", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [], "foreignKeys": []}, {"tableName": "blockTag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `translateName` TEXT NOT NULL, `Id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "translateName", "columnName": "translateName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "Id", "columnName": "Id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["Id"]}, "indices": [{"name": "index_blockTag_name", "unique": true, "columnNames": ["name"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_blockTag_name` ON `${TABLE_NAME}` (`name`)"}], "foreignKeys": []}, {"tableName": "blockUser", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '4df900053eb7dd285d57c197c04f64e7')"]}}