{"formatVersion": 1, "database": {"version": 2, "identityHash": "e7ae3c15ea9227d4f11242a1892f0486", "entities": [{"tableName": "search", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`word` TEXT NOT NULL, `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "word", "columnName": "word", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_search_word", "unique": true, "columnNames": ["word"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_search_word` ON `${TABLE_NAME}` (`word`)"}]}, {"tableName": "history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `title` TEXT NOT NULL, `thumb` TEXT NOT NULL, `isUser` INTEGER NOT NULL DEFAULT false, `count` INTEGER NOT NULL DEFAULT 1, `createdAt` INTEGER NOT NULL, `modifiedAt` INTEGER NOT NULL, PRIMARY KEY(`id`, `isUser`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumb", "columnName": "thumb", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isUser", "columnName": "isUser", "affinity": "INTEGER", "notNull": true, "defaultValue": "false"}, {"fieldPath": "count", "columnName": "count", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedAt", "columnName": "modifiedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id", "isUser"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e7ae3c15ea9227d4f11242a1892f0486')"]}}